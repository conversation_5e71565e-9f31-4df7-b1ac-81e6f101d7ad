import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  try {
    // Create a response object to modify
    const response = NextResponse.next({
      request: {
        headers: request.headers,
      },
    })

    const publicRoutes = ['/login', '/signup', '/auth/', '/access', '/', '/not-found', '/manifest.webmanifest', '/register']
    const pathname = request.nextUrl.pathname

    const isPublicRoute = publicRoutes.some(route => {
      if (route === '/') {
        return pathname === '/';
      }
      return pathname.startsWith(route);
    });

    if (isPublicRoute) {
      return response
    } else {
      // Create supabase client with proper cookie handlers for session refresh
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            get(name: string) {
              return request.cookies.get(name)?.value
            },
            set(name: string, value: string, options: CookieOptions) {
              response.cookies.set({
                name,
                value,
                ...options,
              })
            },
            remove(name: string, options: CookieOptions) {
              response.cookies.set({
                name,
                value: '',
                ...options,
                maxAge: 0,
              })
            },
          },
        }
      )

      // Refresh session if needed - this handles token refresh automatically
      const sessionStartTime = Date.now()
      await supabase.auth.getSession()
      const sessionDuration = ((Date.now() - sessionStartTime) / 1000).toFixed(3)
      
      
      return response
    }

  } catch (error) {
    console.error('Error in updateSession:', error)
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    })
  }
}


    