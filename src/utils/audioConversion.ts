import ffmpeg from 'fluent-ffmpeg'
import { Readable } from 'stream'
import { execSync } from 'child_process'
import { existsSync, statSync } from 'fs'
import { join } from 'path'

// Simplified environment diagnostics (for production)
function logEnvironmentInfo() {
}

// Function to detect FFmpeg path with comprehensive logging
function detectFfmpegPath(): string | null {
  logEnvironmentInfo();
  
  // 1. Check for ffmpeg-static package first (bundled binary)
  try {
    const ffmpegStatic = require('ffmpeg-static');
    
    if (ffmpegStatic && existsSync(ffmpegStatic)) {
      return ffmpegStatic;
    } else {
      // Try fallback paths in node_modules
      const alternativePaths = [
        join(process.cwd(), 'node_modules', 'ffmpeg-static', 'ffmpeg'),
        '/app/node_modules/ffmpeg-static/ffmpeg',
        join(process.cwd(), 'node_modules', 'ffmpeg-static', 'bin', 'linux', 'x64', 'ffmpeg'),
        '/app/node_modules/ffmpeg-static/bin/linux/x64/ffmpeg'
      ];
      
      for (const altPath of alternativePaths) {
        if (existsSync(altPath)) {
          return altPath;
        }
      }
    }
  } catch (error) {
    // ffmpeg-static not available, continue to other methods
  }

  // 2. Check environment variable (for VPS configuration)
  if (process.env.FFMPEG_PATH && existsSync(process.env.FFMPEG_PATH)) {
    return process.env.FFMPEG_PATH;
  }

  // 3. Try to find FFmpeg in system PATH
  try {
    const ffmpegPath = execSync('which ffmpeg', { encoding: 'utf8' }).trim();
    if (ffmpegPath && existsSync(ffmpegPath)) {
      return ffmpegPath;
    }
  } catch (error) {
    // which command failed, continue to next method
  }

  // 4. Check common installation paths
  const commonPaths = [
    '/usr/local/bin/ffmpeg',
    '/usr/bin/ffmpeg',
    '/opt/homebrew/bin/ffmpeg',
    '/bin/ffmpeg'
  ];

  for (const path of commonPaths) {
    if (existsSync(path)) {
      return path;
    }
  }

  return null;
}

// Runtime FFmpeg validation function
export function validateFFmpegAvailability(): { available: boolean; path: string | null; error?: string } {
  try {
    const currentPath = detectFfmpegPath()
    if (currentPath) {
      // Additional runtime verification
      if (existsSync(currentPath)) {
        const stats = statSync(currentPath)
        if (stats.isFile()) {
          return { available: true, path: currentPath }
        } else {
          return { available: false, path: currentPath, error: 'FFmpeg path exists but is not a file' }
        }
      } else {
        return { available: false, path: currentPath, error: 'FFmpeg path no longer exists' }
      }
    } else {
      return { available: false, path: null, error: 'No FFmpeg path detected' }
    }
  } catch (error) {
    return { 
      available: false, 
      path: null, 
      error: `FFmpeg validation error: ${error instanceof Error ? error.message : 'Unknown error'}` 
    }
  }
}

// Set the path to the ffmpeg binary
const ffmpegPath = detectFfmpegPath()
if (ffmpegPath) {
  ffmpeg.setFfmpegPath(ffmpegPath)
} else {
  console.error('FFmpeg not found. Please ensure FFmpeg is installed and accessible.')
}

export interface AudioConversionResult {
  buffer: Buffer
  mimeType: string
  originalFormat: string
  convertedFormat: string
  duration?: number
}

export interface AudioConversionError extends Error {
  originalFormat?: string
  stage?: 'detection' | 'conversion' | 'output'
}

/**
 * Convert audio buffer to M4A AAC format
 * @param inputBuffer - Original audio buffer
 * @param originalMimeType - Original MIME type for debugging
 * @returns Promise<AudioConversionResult>
 */
export async function convertToMP4A(
  inputBuffer: Buffer,
  originalMimeType: string
): Promise<AudioConversionResult> {
  return new Promise((resolve, reject) => {
    try {
      // Check if FFmpeg is available before starting conversion
      if (!ffmpegPath) {
        const error = new Error('FFmpeg not found. Please ensure FFmpeg is installed and accessible in the system PATH.') as AudioConversionError
        error.stage = 'detection'
        reject(error)
        return
      }

      const outputChunks: Buffer[] = []
      let duration: number | undefined

      // Create readable stream from buffer
      const inputStream = new Readable({
        read() {
          this.push(inputBuffer)
          this.push(null) // End of stream
        }
      })

      // Determine original format from MIME type
      const originalFormat = getFormatFromMimeType(originalMimeType)

      const chunks: Buffer[] = []
      
      const command = ffmpeg(inputStream)
        .audioCodec('aac')
        .audioBitrate('64k')
        .audioChannels(1)
        .audioFrequency(44100)
        .format('ipod')
        .outputOptions([
          '-movflags', 'frag_keyframe+empty_moov',
          '-f', 'ipod'
        ])

      // Capture duration information
      command.on('codecData', (data) => {
        if (data.duration) {
          const durationParts = data.duration.split(':')
          if (durationParts.length === 3) {
            const hours = parseInt(durationParts[0])
            const minutes = parseInt(durationParts[1])
            const seconds = parseFloat(durationParts[2])
            duration = hours * 3600 + minutes * 60 + seconds
          }
        }
      })

      // Handle conversion progress (optional, for debugging)
      command.on('progress', (progress) => {
        // console.log(`Audio conversion progress: ${progress.percent}%`)
      })

      // Handle conversion errors
      command.on('error', (err) => {
        const conversionError = new Error(`Audio conversion failed: ${err.message}`) as AudioConversionError
        conversionError.originalFormat = originalFormat
        conversionError.stage = 'conversion'
        reject(conversionError)
      })

      // Handle successful completion
      command.on('end', () => {
        try {
          const outputBuffer = Buffer.concat(chunks)
          
          if (outputBuffer.length === 0) {
            const error = new Error('Audio conversion produced empty output') as AudioConversionError
            error.originalFormat = originalFormat
            error.stage = 'output'
            throw error
          }

          resolve({
            buffer: outputBuffer,
            mimeType: 'audio/x-m4a',
            originalFormat,
            convertedFormat: 'm4a',
            duration
          })
        } catch (error) {
          const outputError = error as AudioConversionError
          outputError.stage = 'output'
          reject(outputError)
        }
      })

      // Get the output stream and handle data
      const stream = command.pipe()
      stream.on('data', (chunk: Buffer) => {
        chunks.push(chunk)
      })
      
      // Handle any stream errors
      stream.on('error', (err) => {
        const streamError = new Error(`Stream error: ${err.message}`) as AudioConversionError
        streamError.originalFormat = originalFormat
        streamError.stage = 'output'
        reject(streamError)
      })

    } catch (error) {
      const initError = new Error(`Failed to initialize audio conversion: ${error instanceof Error ? error.message : 'Unknown error'}`) as AudioConversionError
      initError.stage = 'detection'
      reject(initError)
    }
  })
}

/**
 * Get format name from MIME type for debugging purposes
 */
function getFormatFromMimeType(mimeType: string): string {
  const formatMap: Record<string, string> = {
    'audio/ogg': 'ogg',
    'audio/mpeg': 'mp3',
    'audio/mp4': 'mp4',
    'audio/wav': 'wav',
    'audio/webm': 'webm',
    'audio/aac': 'aac',
    'audio/x-m4a': 'm4a'
  }

  return formatMap[mimeType.toLowerCase()] || 'unknown'
}

/**
 * Check if audio conversion is needed
 * @param mimeType - Original MIME type
 * @returns boolean - true if conversion is needed
 */
export function needsConversion(mimeType: string): boolean {
  // Always convert to ensure consistent M4A AAC format
  // Even if it's already M4A, we want to ensure it's AAC codec with our settings
  return true
}

/**
 * Get file extension for M4A format
 */
export function getM4AExtension(): string {
  return 'm4a'
}

/**
 * Validate audio buffer before conversion
 */
export function validateAudioBuffer(buffer: Buffer, mimeType: string): { valid: boolean; error?: string } {
  if (!buffer || buffer.length === 0) {
    return { valid: false, error: 'Empty audio buffer' }
  }

  if (buffer.length < 100) {
    return { valid: false, error: 'Audio buffer too small (likely corrupted)' }
  }

  if (!mimeType || !mimeType.startsWith('audio/')) {
    return { valid: false, error: 'Invalid MIME type for audio' }
  }

  return { valid: true }
}