'use server'

import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'
import { jwtVerify, createRemoteJWKSet } from 'jose'

export interface AuthResult {
  authenticated: boolean
  authId: string | null
  clientId: string | null
}

export interface JWTClaims {
  sub: string
  email?: string
  app_metadata?: {
    client_id?: string
    [key: string]: any
  }
  user_metadata?: {
    [key: string]: any
  }
  role?: string
  aud?: string
  exp?: number
  iat?: number
  iss?: string
}

// Cache JWKS for better performance
let jwksCache: ReturnType<typeof createRemoteJWKSet> | null = null
let jwksCacheTime = 0
const JWKS_CACHE_TTL = 10 * 60 * 1000 // 10 minutes

function getJWKS() {
  const now = Date.now()
  if (!jwksCache || (now - jwksCacheTime) > JWKS_CACHE_TTL) {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    jwksCache = createRemoteJWKSet(
      new URL(`${supabaseUrl}/auth/v1/.well-known/jwks.json`)
    )
    jwksCacheTime = now
  }
  return jwksCache
}

/**
 * Fast JWT verification using direct JOSE library (no network calls after JWKS cache)
 * This is the fastest method for asymmetric JWT verification
 */
async function verifyJWTDirectly(accessToken: string): Promise<AuthResult> {
  const startTime = Date.now()
  try {
    // First, check if this is actually an asymmetric JWT
    const jwtParts = accessToken.split('.')
    if (jwtParts.length !== 3) {
      throw new Error('Invalid JWT format')
    }

    const header = JSON.parse(Buffer.from(jwtParts[0], 'base64url').toString())

    // Only proceed with JWKS verification for asymmetric algorithms
    if (!header.alg || !['RS256', 'ES256', 'PS256'].includes(header.alg)) {
      throw new Error(`Symmetric algorithm: ${header.alg}`)
    }

    const jwks = getJWKS()
    const { payload } = await jwtVerify(accessToken, jwks, {
      issuer: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/auth/v1`
    })

    const duration = ((Date.now() - startTime) / 1000).toFixed(3)

    return {
      authenticated: true,
      authId: payload.sub!,
      clientId: (payload as any).app_metadata?.client_id || null
    }
  } catch (error) {
    const duration = ((Date.now() - startTime) / 1000).toFixed(3)
    return {
      authenticated: false,
      authId: null,
      clientId: null
    }
  }
}

/**
 * Authentication verification optimized for middleware-refreshed sessions
 * 1. Direct JWT verification (fastest - no network calls after JWKS cache)
 * 2. Fallback to getClaims() for symmetric JWTs
 * Note: getUser() removed since middleware guarantees fresh sessions
 */
export async function verifyAuth(): Promise<AuthResult> {
  const overallStartTime = Date.now()
  
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Get session to extract access token
    const sessionStartTime = Date.now()
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    const sessionDuration = ((Date.now() - sessionStartTime) / 1000).toFixed(3)
    
    if (sessionError || !session?.access_token) {
      // Should be rare since middleware refreshes sessions
      return {
        authenticated: false,
        authId: null,
        clientId: null
      }
    }


    // Strategy 1: Try direct JWT verification (fastest for asymmetric JWTs)
    const directResult = await verifyJWTDirectly(session.access_token)

    if (directResult.authenticated) {
      const totalDuration = ((Date.now() - overallStartTime) / 1000).toFixed(3)
      return directResult
    }

    // Strategy 2: Fallback to getClaims()
    const claimsStartTime = Date.now()
    const { data: claimsResponse, error: authError } = await supabase.auth.getClaims()
    const claimsDuration = ((Date.now() - claimsStartTime) / 1000).toFixed(3)

    if (!authError && claimsResponse?.claims?.sub) {
      const totalDuration = ((Date.now() - overallStartTime) / 1000).toFixed(3)
      return {
        authenticated: true,
        authId: claimsResponse.claims.sub,
        clientId: claimsResponse.claims.app_metadata?.client_id || null
      }
    }

    const totalDuration = ((Date.now() - overallStartTime) / 1000).toFixed(3)
    
    return {
      authenticated: false,
      authId: null,
      clientId: null
    }

  } catch (error) {
    const totalDuration = ((Date.now() - overallStartTime) / 1000).toFixed(3)
    return {
      authenticated: false,
      authId: null,
      clientId: null
    }
  }
}


