'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// Import default translations to prevent hydration issues
import enTranslations from '../locales/en.json'
import kmTranslations from '../locales/km.json'

// Define the language type
export type Language = 'km' | 'en'

// Define the context type
interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

// Create the context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// Pre-loaded translations to avoid async loading issues
const preloadedTranslations = {
  en: enTranslations,
  km: kmTranslations
}

// Browser language detection helper
const detectBrowserLanguage = (): Language => {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') return 'en'
  
  const browserLang = navigator.language.toLowerCase()
  if (browserLang.startsWith('km')) return 'km'
  if (browserLang.startsWith('en')) return 'en'
  
  // Default to English for international audience
  return 'en'
}

// Create the provider component
export function LanguageProvider({ children }: { children: ReactNode }) {
  // Default to English for SSR compatibility
  const [language, setLanguageState] = useState<Language>('en')
  
  // Use preloaded translations instead of async loading
  const [translations, setTranslations] = useState<Record<string, string>>(preloadedTranslations.en)
  const [isLoading, setIsLoading] = useState(false) // No longer async loading

  // Function to update translations synchronously
  const updateTranslations = (lang: Language) => {
    setTranslations(preloadedTranslations[lang])
  }

  // Update translations when language changes
  useEffect(() => {
    updateTranslations(language)
  }, [language])

  // Load language preference from localStorage or cookie on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        // Try to get language from localStorage first (highest priority)
        let savedLang = localStorage.getItem('uiLanguage') as Language

        // If not in localStorage, try to get from cookie as fallback
        if (!savedLang || (savedLang !== 'km' && savedLang !== 'en')) {
          const cookies = document.cookie.split(';')
          const langCookie = cookies.find(cookie => cookie.trim().startsWith('uiLanguage='))
          if (langCookie) {
            savedLang = langCookie.split('=')[1].trim() as Language
          }
        }

        // If we have a saved preference, use it
        if (savedLang && (savedLang === 'km' || savedLang === 'en')) {
          setLanguageState(savedLang)
          document.documentElement.lang = savedLang
        } else {
          // No saved preference - detect browser language for new users
          const detectedLang = detectBrowserLanguage()
          setLanguageState(detectedLang)
          document.documentElement.lang = detectedLang
        }
      } catch (error) {
        console.error('Error loading language preference:', error)
        // Default to Khmer in case of error
        document.documentElement.lang = 'km'
      }
    }
  }, [])

  // Handle language change
  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    if (typeof window !== 'undefined') {
      try {
        // Set the language in localStorage
        localStorage.setItem('uiLanguage', lang)

        // Update HTML lang attribute
        document.documentElement.lang = lang

        // Also set a cookie as a fallback for browsers with localStorage issues
        document.cookie = `uiLanguage=${lang}; path=/; domain=.chhlatbot.com; max-age=31536000; SameSite=Lax` // 1 year expiry

        // console.log('Language set in context:', lang)
      } catch (error) {
        console.error('Error setting language:', error)
      }
    }
  }

  // Translation function - returns actual translation or key as fallback
  const t = (key: string): string => {
    // Return translation or fallback to key if not found
    return translations[key] || key
  }

  const value = {
    language,
    setLanguage,
    t
  }

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>
}

// Hook to use the language context
export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

