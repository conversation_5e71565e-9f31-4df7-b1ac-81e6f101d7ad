{"features": "Features", "pricing": "Pricing", "how_it_works": "How It Works", "faq": "FAQ", "login": "Log In", "dashboard": "Dashboard", "welcome_dashboard": "<span class=\"text-jade-purple\">Dashboard</span>", "dashboard_header": "Dashboard", "account_setup_incomplete": "Account Setup Incomplete", "account_setup_message": "Your account doesn't have a client profile setup yet. Some features may be limited.", "email": "Email:", "contact_support": "Please contact support to complete your account setup.", "messages": "Messages", "total": "total", "plan": "Plan", "bill": "Expire:", "expired": "Expired:", "ai_brain": "Chhlat Brain", "text": "Text", "voice": "Voice", "image": "Image", "connects": "Connects", "knowledgebase": "Knowledge Base", "socialchannels": "Social Channels", "connect_your_social_platforms": "Connect Your Social Media Platforms", "train_your_chhlatbot": "Train Your ChhlatBot", "plan_expired_warning": "Your plan is expired, please make a payment or upgrade your plan to continue using your ChhlatBot, thank you", "usage_limit_warning": "You have reached your usage limit, please add more messages or upgrade plan to continue using your ChhlatBot, thank you", "usage_limit_reached_error": "You have reached your message limit. Please contact the team to add more messages for your ChhlatBot to operate normally.", "failed_check_usage_limits": "Failed to check usage limits. Please try again.", "connection_limit_reached_error": "You've reached your connection limit ({limit}). Please upgrade your plan to connect more platforms.", "enter_valid_token": "Please enter a valid access token for {platform}.", "enter_valid_domain": "Please enter a valid domain name for {platform}.", "enter_valid_page_id": "Please enter a valid Page ID for Instagram.", "failed_connect_platform": "Failed to connect to {platform}, please provide a valid token", "client_id_not_found": "Client ID not found. Please ensure you are properly logged in.", "invalid_token_error": "Invalid credentials or username provided for {platform}. Please check and try again.", "already_used_for_trial_error": "This {platform} page or account has already been used for a free trial.", "already_use_for_trial_error": "This {platform} page or account has already been used for a free trial.", "still_connected_error": "This {platform} page or account is currently connected to another ChhlatBot account.", "connection_error": "Something went wrong. Please try again.", "hero_title": "ChhlatBot: <span class=\"text-jade-purple-dark\">Automated Customer Replies</span> on Social Media", "hero_title_part1": "Automated Customer Replies", "hero_title_part2": "on Social Media", "hero_subtitle": "ChhlatBot provides an automated reply system for social media, handling 90% of repetitive customer inquiries with AI across Facebook, Instagram, Telegram, and Website. It can understand text and audio messages in over 100 languages, including Khmer, offering enterprise-level customer service automation for small and medium-sized businesses at minimal cost. (TikTok integration coming soon!)", "try_free": "Try 1-Month Free", "go_to_dashboard": "Go to Dashboard", "learn_more": "Learn More", "upgrade": "Upgrade", "unlock": "Unlock", "premium": "Premium", "current": "Current", "usage": "Usage", "progress": "Progress", "base": "Base", "train": "Train", "your": "Your", "ai": "AI", "social": "Social", "platforms": "Platforms", "media": "Media", "value_title": "Free Up <span class=\"text-jade-purple\">Time</span> & <span class=\"text-jade-purple\">Never Miss</span> A Message", "value_title_part1": "Free Up", "value_title_part2": "Time", "value_title_part3": "&", "value_title_part4": "Never Miss", "value_title_part5": "A Message", "value_subtitle": "Let ChhlatBot take care of repetitive customer inquiries while you focus on growing your business", "benefit_instant_title": "Instant Replies", "benefit_instant_desc": "ChhlatBot responds to customer inquiries in real-time 24/7 without delay", "benefit_smart_title": "Smart ChhlatBot", "benefit_smart_desc": "ChhlatBot can send and understand text, voice, and image messages in the Khmer and other languages", "benefit_analytics_title": "Analytics", "benefit_analytics_desc": "Understand customer behavior and trends with powerful analytics that drive smarter business decisions", "benefit_inbox_title": "Inbox Marketing", "benefit_inbox_desc": "Connect with your audience more effectively through targeted messaging across all your chat channels", "benefit_control_title": "Full Control", "benefit_control_desc": "Control how ChhlatBot replies in text, voice, or images to reflect your brand's tone", "benefit_notification_title": "Chhlat Notify", "benefit_notification_desc": "Get Telegram alerts for urgent tasks and daily tips to make your ChhlatBot smarter", "pricing_title": "<span class=\"text-jade-purple\">Pricing</span>", "pricing_subtitle": "Choose the plan that best fits your social media automation needs", "pricing_promo": "New users get 1-month free trial", "billing_1month": "1-Month", "billing_3month": "3-Month", "recommended": "RECOMMENDED", "messages_label": "Messages:", "channels_label": "Platforms:", "features_label": "Features:", "select_plan": "Select Plan", "pricing_note": "*Note: Discounts available with longer commitments.", "billed_for": "Billed $${amount} for ${months} months", "enterprise_title": "Enterprise Solutions", "enterprise_desc": "Custom plans available for high-volume needs and enterprise teams that want custom integrations", "get_in_touch": "Get in Touch", "addon_desc": "Super affordable and flexible. Just $1 for every additional 200 messages. Enjoy even lower rates with higher volumes", "for": "for", "coming_soon_plan": "Coming Soon", "subscription": "Subscription", "plans": "Plans", "current_plan": "Current Plan", "no_plan": "No Plan", "renews_on": "Renews on {date}", "no_billing_info": "No billing information available", "not_available": "Not available", "pay": "Pay", "change_plan": "Change Plan", "plan_basic": "Basic", "plan_pro": "Pro", "plan_premium": "Premium", "plan_addon": "Flexible Add-Ons", "addon_description": "Scale with your needs", "feature_khmer_english_text": "Support 100+ Languages in Text", "feature_text_voice_image": "Replies in Text, Voice, and Image", "feature_ai_brain_500": "500 Chhlat Brain", "feature_entries_50": "50 Chhlat Tag", "feature_dashboard": "Real-Time Dashboard", "feature_setup": "Guided Self-Setup", "feature_telegram_alert": "Instant Alert Via Telegram", "feature_daily_report": "Chhlat Brain Daily Update Report", "feature_everything_intern": "Everything in Intern", "feature_voice": "Support 100+ Languages in Voice", "feature_ai_brain_1000": "1,000 Chhlat Brain", "feature_entries_100": "100 Chhlat Tag", "feature_chat_history": "Daily Analytic Report", "feature_everything_assistant": "Everything in Assistant", "feature_image_understanding": "Support Image Understanding", "feature_ai_brain_1500": "1,500 Chhlat Brain", "feature_entries_150": "150 Chhlat Tag", "feature_inbox_marketing": "Chat Inbox Marketing", "feature_advanced_analytics": "Advanced Analytics", "how_title": "<span class=\"text-jade-purple\">Simple</span> Setup Process", "how_subtitle": "Get your intelligent social media assistant up and running in minutes", "step": "Step", "step1_title": "Connect Your Social Accounts", "step1_desc": "Link your business accounts from Facebook, Telegram, Instagram, and Website to ChhlatBot's dashboard", "step2_title": "Train Your ChhlatBot", "step2_desc": "Share your business knowledge through text, voice, or image, and customize how ChhlatBot responds to your customers", "step3_title": "Monitor Your Dashboard", "step3_desc": "Let the ChhlatBot handle routine inquiries while you monitor results and step in when needed.", "faq_title": "Frequently <span class=\"text-jade-purple\">Asked</span> Questions", "faq_subtitle": "Everything you need to know about our social media automation", "faq_more_questions": "Still have questions?", "faq_contact_support": "Contact our support team", "faq1_question": "Can we test it before buying?", "faq1_answer": "Of course! We provide a 1-month free trial. You can try all the features and see how it works for your business.", "faq2_question": "Does it take time to set up?", "faq2_answer": "Not really—it takes less than 5 minutes to set up. Once you've finished providing your business knowledge, you can start using it right away. While it's a self-setup process, we offer plenty of helpful resources. And if you ever need assistance, our team is here to support you.", "faq3_question": "How does ChhlatBot work with my social media accounts?", "faq3_answer": "ChhlatBot doesn't require your social media credentials or admin access. You simply provide your page access token — everything stays under your control, giving you more privacy and reducing risk to your business pages.", "faq4_question": "Can I customize the ChhlatBot's responses to match my brand voice?", "faq4_answer": "Absolutely! You can train the ChhlatBot with your specific products, services, and brand tone. ChhlatBot can automatically send messages to your customers using text and images.", "faq5_question": "What do you mean by supporting English & Khmer voice?", "faq5_answer": "ChhlatBot can understand more than 100 languages, includingKhmer, in both text and voice formats.", "faq6_question": "What happens when ChhlatBot can't answer a customer's question?", "faq6_answer": "If ChhlatBot encounters a question it isn't confident about, it will first clarify the question with the customer. If it still can't answer, ChhlatBot will automatically escalate the conversation to you or your team via Telegram. You can step in seamlessly while ChhlatBot continues handling other inquiries.", "faq7_question": "Can I use ChhlatBot on multiple social media platforms with one subscription?", "faq7_answer": "Yes! Depending on your plan, you can connect multiple social media platforms. The Basic plan supports 1 platform, the Pro plan supports up to 3, and the Premium plan gives you full access to all available platforms.", "cta_title": "Ready to <span class=\"text-jade-purple\">Automate</span> Your Social Media Messages?", "cta_title_part1": "Ready to", "cta_title_part2": "Automate", "cta_title_part3": "Your Social Media Messages?", "cta_subtitle": "Join thousands of businesses using ChhlatBot to manage customer communications across Facebook, Telegram, Instagram, WhatsApp, and Website.", "cta_try_free": "Try 1-Month Free", "cta_dashboard": "Go to Dashboard", "footer_description": "Automate your social media customer service with ChhlatBot. Handle messages across Facebook, Telegram, Instagram, and Website from one dashboard.", "footer_rights": "All rights reserved.", "footer_privacy": "Privacy Policy", "footer_terms": "Terms of Service", "footer_product": "Product", "footer_company": "Company", "footer_features": "Features", "footer_pricing": "Pricing", "footer_how_it_works": "How It Works", "footer_faq": "FAQ", "footer_about": "About Us", "footer_blog": "Blog", "footer_careers": "Careers", "footer_contact": "Contact", "footer_docs": "Docs", "terminal_active": "Active", "terminal_today": "TODAY", "terminal_write_message": "Write a message...", "terminal_customer_1": "Hi! I'm running a business and drowning in customer messages. Can your AI actually handle real customer service conversations?", "terminal_bot_1": "Hello! Yes, ChhlatBot can answer questions about your products, company policies, services, handle customer issues, and collect order or appointment information - then notify you immediately via Telegram. I'm like having a smart assistant working 24/7. What's overwhelming you most?", "terminal_customer_2": "My customers ask everything - product details, shipping policies, complaints, order changes. Can you really handle all of that without sounding robotic?", "terminal_bot_2": "I understand context and handle chat in a natural conversation flow. When someone says 'I want to get that item' - I know exactly what they mean, if not I will ask for clarification.", "terminal_customer_3": "That's smart! But I have customers who speak Khmer and English. How do you handle language switching?", "terminal_bot_3": "I automatically detect and respond in their language - seamlessly switching between Khmer and English mid-conversation. Your Khmer customers get the same quality service as English speakers, 24/7. In addition, I can understand over 100 languages in text and voice (including Khmer).", "terminal_customer_4": "This sounds too good to be true. What happens when you don't know something or make mistakes?", "terminal_bot_4": "When I cannot find an answer due to lack of business knowledge you haven't trained me on, I'm honest about my limitations. When I'm unsure, I will ask for clarification. If I still cannot resolve it, I immediately send you a notification via Telegram, and at the same time provide the customer the option to connect with you. No fake answers, no guessing - just honest, professional service.", "terminal_customer_5": "This could actually scale my business! How do you learn about my specific business and products?", "terminal_bot_5": "Regarding business knowledge, you are 100% in control. You provide me with training data about your business and products/services. Therefore, I won't respond to customers about what you haven't provided. Moreover, each night I will send you all those missed questions from customers, so you can update my knowledge base.", "terminal_customer_6": "Oh ok, then how do I get started with your service?", "terminal_bot_6": "You can register on our website, connect your social accounts, upload your business information, and you can enable ChhlatBot for your business instantly - no waiting time needed. And currently we provide a 1 month free trial for new customers with no upfront payment or credit card needed.", "payment_success_title": "Payment Successful!", "payment_success_message": "Your payment has been processed successfully. Your subscription has been updated.", "payment_cancelled_title": "Payment Cancelled", "payment_cancelled_message": "Your payment was cancelled. No charges were made to your account.", "redirecting_in": "Redirecting in", "seconds": "seconds", "try_again": "Try Again", "processing_payment": "Processing Payment...", "redirecting_to_payment": "Redirecting to payment gateway...", "payment_note": "You will be redirected to the ABA PayWay payment gateway to complete your payment.", "payment_security_note": "Your payment information is secure and encrypted.", "pay_now": "Pay Now", "cancel": "Cancel", "changing_language": "Changing language...", "please_wait": "Please wait", "loading_dashboard": "Loading dashboard...", "back": "Back", "connect_accounts": "Connect Accounts", "connected_platforms": "Connected Platforms", "manage_connected_platforms": "Manage your connected platforms", "edit": "Edit", "facebook_messenger": "Facebook Messenger", "instagram": "Instagram", "instagram_dm": "Instagram DM", "telegram": "Telegram", "web_api": "Web API", "tiktok": "TikTok", "name": "Name", "domain": "Domain", "not_set": "Not set", "no_platforms_connected": "No platforms connected yet. Connect a platform to get started.", "select_platform": "Select Platform", "choose_platform_details": "Choose a platform to view its connection details.", "connection_limit_reached": "Connection limit reached, upgrade your plan!", "selected_platform": "Selected Platform", "connect_platform_message": "Connect your {platform} account to enable ChhlatBot features.", "copy_webhook_provide_token": "Copy webhook URL and provide your access token", "copy_webhook_provide_token_id": "Copy webhook URL and provide your access token and ID", "connect_telegram_bot": "Connect your Telegram bot via API token", "telegram_bot": "Telegram Bot", "telegram_business": "Telegram Business", "use_bot_token": "Use Bot Token", "use_username": "Use Username", "choose_connection_type": "Choose your connection type", "bot_name": "Bot Name", "your_telegram_username": "Your Telegram Username", "enter_valid_telegram_username": "Please enter a valid Telegram username", "coming_soon": "Coming soon", "web_api_coming_soon": "Web API integration will be available soon", "more_platforms_coming_soon": "More platform integrations coming soon", "connected": "Connected", "connecting": "Connecting...", "connect": "Connect", "enter_access_token": "Enter Access Token", "enter_id": "Enter ID", "enter_telegram_bot_token": "Enter Telegram Bot API Token", "telegram_username_placeholder": "@yourusername", "successfully_connected": "Successfully connected {platform}!", "platform_status_changed": "{platform} is now {status}", "web_api_coming_soon_short": "Web API (Coming Soon)", "tiktok_coming_soon_short": "<PERSON><PERSON><PERSON><PERSON> (Coming Soon)", "unexpected_error": "An unexpected error occurred. Please try again later.", "failed_load_connection_data": "Failed to load connection data", "failed_initialize_connection_data": "Failed to initialize connection data", "failed_connect_generic": "Failed to connect {platform}", "unexpected_server_response": "Unexpected response from server when connecting {platform}", "error": "Error", "success": "Success!", "loading": "Loading", "please_wait_processing": "Please wait while we're connecting...", "confirm_status_change": "Confirm Status Change", "confirm_connection": "Confirm Connection", "confirm_action_platform": "Are you sure you want to {action} {platform}?", "confirm_connect_web": "Are you sure you want to connect {platform}?", "confirm_connect_platform": "Are you sure you want to connect {platform}?", "enable": "Enable", "disable": "Disable", "edit_connections": "Edit Connections", "loading_connection_details": "Loading your connection details...", "confirm_update": "Confirm Update", "confirm_update_web": "Are you sure you want to update {platform}? This will register your domain with the server.", "confirm_update_platform": "Are you sure you want to update {platform}? This will send your token to the server.", "update": "Update", "disconnecting": "Disconnecting...", "confirm_disconnect": "Confirm Disconnect", "please_wait_disconnect": "Please wait while we disconnect {platform}...", "confirm_disconnect_message": "Are you sure you want to disconnect {platform}?", "disconnect": "Disconnect", "processing": "Processing...", "manage_facebook_connection": "Manage your Facebook Messenger connection", "manage_instagram_connection": "Manage your Instagram DM connection", "manage_telegram_connection": "Manage your Telegram bot connection", "manage_web_connection": "Manage your Web API connection", "no_platforms_connected_edit": "No platforms connected yet. Go back to connect a platform first.", "connect_platforms": "Connect Platforms", "business_insights": "Business Insights", "add_business_info": "Add information about your business to ChhlatBot.", "no_photos_found": "No photos found", "search_photo_placeholder": "Search for a photo by ID...", "enter_question": "Enter question...", "enter_reply": "Enter reply...", "paste_audio_code": "Paste your audio code from Telegram bot", "switch_to_audio_mode": "Switch to Audio Mode", "audio_mode_warning": "Switching to audio mode will clear your current reply text. Do you want to continue?", "audio_code_incorrect": "Audio code is incorrect or not found", "add": "Add", "recently_added": "Recently Added", "updating": "Updating...", "question": "Question ", "reply": "Reply ", "delete_item": "Delete Item", "delete_confirmation": "Are you sure you want to delete this item? This action cannot be undone.", "photo_gallery": "Chhlat Tag", "business_insight": "Chhlat Brain", "intro_outro": "Custom Messages", "start_recording": "Start Recording", "stop_recording": "Stop Recording", "manage": "Manage", "no_questions_update": "No questions to update. Add some questions and answers first.", "processing_additions": "Processing additions...", "finalizing": "Finalizing...", "update_success": "Successfully updated {count} question{plural}.", "confirm_update_questions": "You are about to add {count} question{plural} to your knowledge base?", "saving": "Saving", "brain": "Chhlat Brain", "knowledge": "Knowledge", "management": "Management", "actions": "Actions", "confirm": "Confirm", "done": "Done", "close": "Close", "photos": "Photos", "save": "Save", "delete": "Delete", "ready": "Ready", "recording": "Recording...", "intros_outros": "Custom Messages", "intro": "Intro", "outro": "Outro", "intro_message": "Intro Message", "outro_message": "Outro Message", "intro_description": "Set up a welcome message that will be sent at the beginning of each chat.", "outro_description": "Set up a closing message that will be sent at the end of each chat.", "edit_intro_message": "Edit Intro Message", "edit_outro_message": "Edit Outro Message", "enter_welcome_message": "Enter welcome message...", "enter_closing_message": "Enter closing message...", "save_intro_message": "Save Intro Message", "save_outro_message": "Save Outro Message", "save_intro_confirmation": "Are you sure you want to save this intro message?", "save_outro_confirmation": "Are you sure you want to save this outro message?", "discard_changes": "Discard Changes", "unsaved_intro_changes": "You have unsaved changes to your intro message. Are you sure you want to discard these changes?", "unsaved_outro_changes": "You have unsaved changes to your outro message. Are you sure you want to discard these changes?", "keep_editing": "Keep Editing", "discard": "Discard", "language_guide_title": "Language Guide", "language_guide_message": "Please provide your information (both text and audio) in", "tutorial_guides": "Tutorial Guides", "telegram_guide_description": "Generate audio codes for voice messages", "documentation": "Documentation", "documentation_guide_description": "Step-by-step page navigation guide", "video_tutorial": "Video Tutorial", "video_guide_description": "Video tutorial for this page", "help_video_tutorial_description": "Please watch the video tutorial below if you have any questions. To use voice messages as replies, send a voice message via the Telegram bot to get an audio code.", "language_tip": "Language Tip", "need_help": "Need Help?", "audio_code_generator": "Audio Code Generator", "step_by_step_guide": "Step-by-Step Guide", "video_walkthrough": "Video Walkthrough", "quick_tip": "Quick tip", "friendly_language_message": "Write your message in", "language_helper_text": "so your bot speaks the right language", "new_to_this": "New to this?", "weve_got_you_covered": "We've got you covered!", "need_audio_message": "Need audio for your message?", "chat_friendly_bot": "Chat with our friendly bot to get audio codes", "instant_setup": "Instant setup", "feeling_lost": "Feeling lost?", "simple_guide_description": "Check out our simple guide", "takes_2_minutes": "Takes 2 minutes", "prefer_watching": "Prefer watching?", "quick_video_description": "Quick video walkthrough", "under_3_minutes": "Under 3 minutes", "send_audio": "Send Audio", "no_image": "No Image", "no_images_available": "No images available", "click_to_zoom_out": "Click to zoom out", "photo_page_title": "Chhlat Tag", "add_photos": "Add", "photo_id": "Chhat Tag ID", "photo_id_title": "Cannot be changed", "enter_photo_id": "Enter a unique ID for this photo", "drop_files_here": "Drop files here or click to browse", "max_files": "Maximum 4 files, 5MB each (JPG or PNG)", "add_more": "Add more", "processing_image": "Processing...", "upload_photos": "Upload", "update_photo": "Update", "no_photos_added": "No photos added yet", "click_add_photos": "Click \"Add\" to upload your first Chhlat Tag", "search_photo_id": "Search Chhlat Tag by ID...", "photo_count": "photo(s)", "previous_page": "Previous", "next_page": "Next", "showing_text": "Showing", "of_text": "of", "try_different_search": "Try a different search term", "discard_changes_title": "Discard Changes?", "photos_selected": "You have {count} photo{plural} selected. If you continue, your changes will be lost.", "discard_button": "Discard Changes", "upload_confirm": "Upload {count} Photo{plural}?", "upload_confirm_message": "Are you sure you want to upload {count} photo{plural} with ID \"{id}\"?", "confirm_upload": "Confirm Upload", "processing_upload": "Processing...", "complete_text": "complete", "success_text": "Success!", "error_text": "Error", "existing_photo": "Existing Photo", "loading_photos": "Loading Chhlat Tags...", "unsaved_changes": "You have unsaved changes. Are you sure you want to cancel? Your changes will be lost.", "update_photo_confirm": "Update Photo?", "update_photo_message": "Are you sure you want to update this photo? This will save all your changes.", "yes_update": "Yes, Update", "deleting": "Deleting...", "delete_photo": "Delete Photo?", "delete_photo_confirm": "Are you sure you want to delete the photo with ID \"{id}\"? This action cannot be undone.", "deleting_photo": "Deleting photo and associated files...", "delete_success_title": "Deleted!", "delete_success_message": "Photo has been successfully deleted.", "yes_delete": "Yes, Delete", "remove_photo_title": "Remove Photo", "remove_photo_message": "Are you sure you want to remove this photo from the list?", "yes_remove": "Yes, Remove", "cannot_delete_photo": "Cannot Delete Photo", "photo_in_use": "This Chhlat Tag cannot be deleted because it is being used in one or more Chhlat Brain items.", "photo_in_use_welcome": "This Chhlat Tag cannot be deleted because it is being used in Chhlat Brain items and/or the Welcome page.", "linked_questions": "Linked questions", "linked_welcome": "Used in Welcome page", "click_zoom_out": "Click to zoom out", "file_size_error": "File size exceeds 5MB limit ({size}MB)", "file_type_error": "Only JPG and PNG images are allowed", "remaining_slots_error": "You can only add {count} more image(s).", "process_error": "Failed to process image. Please try again.", "select_file_id_error": "Please select at least one file and enter a photo ID", "photo_id_exists": "Photo ID already exists", "photo_limit_error": "You can only upload {count} more photos. Please upgrade your plan or delete some photos.", "client_id_error": "Client ID not found. Please ensure you are properly logged in.", "auth_error": "User authentication failed. Please log in again.", "url_error": "Failed to get URL for uploaded image", "upload_error": "Error uploading file {count}: {message}", "save_error": "Error saving photo record: {message}", "preparing_upload": "Preparing to upload photos...", "uploading_photos": "Uploading photos...", "uploading_photo": "Uploading photo {current} of {total}...", "saving_photo_info": "Saving photo information...", "photos_uploaded": "Photos uploaded successfully!", "upload_complete": "Upload complete! Your photos have been added.", "updating_related_items": "Updating related knowledge base items...", "kb_title": "Your Business Information", "kb_subtitle": "View and manage your Chhlat Brain", "kb_search_questions": "Search Chhlat Brain...", "kb_loading_questions": "Loading Chhlat Brain...", "kb_try_again": "Try Again", "kb_no_questions_found": "No Chhlat Brain found matching your search criteria.", "kb_no_questions_added": "No Chhlat Brain added to your knowledge base yet.", "kb_question_column": "Question", "kb_reply_column": "Reply", "kb_edit_question": "Edit this Chhlat Brain", "edit_reply": "Edit Reply", "kb_delete_question": "Delete this Chhlat Brain", "kb_delete_confirmation": "Are you sure you want to delete this Chhlat Brain?", "kb_question_cannot_be_edited": "Question cannot be edited, delete and add new if needed", "kb_search_photo": "Search for a Chhlat Tag by ID...", "kb_no_photo": "No Image", "kb_discard_changes": "Discard Changes", "kb_unsaved_changes": "You have unsaved changes to this Chhlat Brain. Are you sure you want to discard these changes?", "kb_keep_editing": "Keep Editing", "kb_discard": "Discard", "click_to_edit": "Click to edit", "kb_recording": "Recording...", "kb_ready": "Ready", "change_knowledge_base": "Update Chhlat Brain", "changing_knowledge_base": "Updating Chhlat Brain", "change_button": "Update", "changing": "Updating", "change_business_insights": "Update your Chhlat Brain.", "change_question_confirmation": "You are about to update your Chhlat Brain.", "delete_audio_warning": "Are you sure you want to delete this audio", "delete_audio_file": "Delete Audio", "settings": "Settings", "settings_language_title": "ChhlatBot Language", "settings_language_description": "This setting defines your ChhlatBot's main language. All your questions and replies should be in this language.", "settings_language_explanation": "For incoming chat, ChhlatBot can understand text in over 100+ languages. If the incoming chat matches your primary language here, ChhlatBot will reply in this primary language. Other languages will default to English reply for text.", "settings_language_english_note": "If you select English as your primary language, the secondary language will be set to Khmer by default.", "settings_language_active": "Active", "settings_change_language": "Change Language", "settings_change_password_title": "Change Password", "settings_current_password": "Current Password", "settings_new_password": "New Password", "settings_confirm_password": "Confirm New Password", "settings_update_password": "Update Password", "settings_updating": "Updating...", "settings_account_title": "Account", "settings_logout": "Log out", "settings_logging_out": "Logging out...", "settings_change_language_confirm_title": "Change ChhlatBot Language?", "settings_change_language_confirm_message": "You are about to change your ChhlatBot's main language to {language}. This affects how your ChhlatBot processes questions and replies. You should provide all content in {language} after this change.", "settings_final_confirmation": "Final Confirmation", "settings_language_change_warning": "By changing to {language}, you confirm that:", "settings_language_change_point1": "You will provide all questions and replies in {language}", "settings_language_change_point2": "All your voice record should be in {language}", "settings_language_change_point3": "Your ChhlatBot will be optimized for {language} conversations", "settings_language_change_point4_english": "If the chat is not in English, Khmer will be used for text reply", "settings_language_change_point4_other": "If the chat is not in {language}, English will be used for text reply", "settings_language_change_final_question": "Are you absolutely sure you want to change your ChhlatBot's language?", "settings_yes_change": "Yes, Change", "settings_confirm_logout": "Confirm <PERSON>ut", "settings_logout_message": "Are you sure you want to log out of your account?", "settings_yes_logout": "Yes, Log Out", "settings_password_updated": "Password Updated!", "settings_password_success_message": "Your password has been successfully changed.", "settings_language_updated": "Language Updated!", "settings_language_success_message": "ChhlatBot language has been successfully changed.", "settings_passwords_no_match": "New passwords do not match", "settings_current_password_incorrect": "Current password is incorrect", "settings_password_update_failed": "Failed to update password", "settings_continue": "Continue", "access_dashboard_title": "Access Dashboard", "access_email_label": "Email", "access_email_placeholder": "Enter your email", "access_password_label": "Password", "access_password_placeholder": "Enter your password", "access_button": "Access", "access_loading": "Accessing...", "access_no_account": "Don't have an account?", "access_register": "Register", "register_title": "Register for ChhlatBot", "register_select_plan": "Select your plan", "register_free_trial": "All new users get a 1-month free trial", "register_choose_username": "Choose a username for your account", "register_username_description": "This will be your unique identifier in the system", "register_username_label": "Username", "register_username_placeholder": "Enter your username", "register_username_rules": "Username can only contain letters, numbers, and underscores", "register_select_language": "Select Your Main Language", "register_language_main_purpose": "Please choose the main language you will use to provide questions and replies.", "register_language_point1": "ChhlatBot understands 100+ languages from your customers in text.", "register_language_point2": "When your customer's message matches your main language, ChhlatBot will reply in that language.", "register_language_point3": "If your customer uses another language, ChhlatBot will reply in English (or Khmer if you select English as your main language).", "register_language_point4": "Also supports more reply languages. Please contact our team for details.", "register_select_sector": "Select your business sector", "register_agree_to": "I agree to the", "register_terms_and_conditions": "Terms and Conditions", "register_sector_description": "This helps us customize ChhlatBot for your specific industry", "register_back": "Back", "register_next": "Next", "register_checking": "Checking...", "register_register_button": "Register", "register_registering": "Registering...", "register_have_account": "Already have an account?", "register_login": "Log in", "register_modal_title": "Registration Complete!", "register_modal_subtitle": "Follow the steps below to activate your account", "register_modal_code_label": "Your registration code:", "register_modal_copied": "Copied to clipboard!", "register_modal_expire_text": "This code will expire in", "register_modal_instruction1": "Please copy this code and send it to our Telegram bot to complete your registration.", "register_modal_instruction2": "Please note: The Telegram account you use to send this code will be the one we use for all future notifications and important updates from our service. Make sure to use the Telegram account that you primarily intend to manage and receive communications from us.", "register_modal_telegram": "Open Telegram", "register_modal_final_instruction": "After sending the code to our Telegram bot, you will receive your login credentials.", "register_modal_login": "Log in", "register_basic_messages": "500 messages", "register_basic_channels": "1 platform", "register_basic_support": "Text support only (100+ languages)", "register_pro_messages": "1,000 messages", "register_pro_channels": "3 platforms", "register_pro_support": "Text & Voice support (100+ languages)", "register_options": "options", "toggle_platform_confirmation": "Toggle Platform Status", "toggle_platform_warning": "Are you sure you want to change the status of this platform?", "sector_retail_and_commerce": "Retail & Commerce", "sector_beauty_and_wellness": "Beauty & Wellness", "sector_transport_and_logistics": "Transport & Logistics", "sector_finance_and_legal": "Finance & Legal", "sector_ict_and_digital_services": "ICT & Digital Services", "sector_manufacturing_and_industrial": "Manufacturing & Industrial", "sector_food_and_beverage": "Food & Beverage", "sector_tourism_and_hospitality": "Tourism & Hospitality", "sector_real_estate_and_construction": "Real Estate & Construction", "sector_medical_and_health": "Medical & Health", "sector_education_and_training": "Education & Training", "sector_entertainment_and_media": "Entertainment & Media", "sector_services": "Professional & General Services", "sector_agriculture_and_agribusiness": "Agriculture & Agribusiness", "sector_community_and_religious": "Community & Religious", "sector_others": "Others", "subsector_cosmetics_and_skincare_shop": "Cosmetics & Skincare Shop", "subsector_clothing_and_fashion_store": "Clothing & Fashion Store", "subsector_electronics_and_phone_shop": "Electronics & Phone Shop", "subsector_convenience_store_minimart": "Convenience Store & Minimart", "subsector_grocery_store_market": "Grocery Store & Market", "subsector_home_appliances_store": "Home Appliances Store", "subsector_furniture_store": "Furniture Store", "subsector_jewelry_and_accessories_shop": "Jewelry & Accessories Shop", "subsector_baby_and_kids_store": "Baby & Kids Store", "subsector_sports_equipment_store": "Sports Equipment Store", "subsector_bookstore_and_stationery": "Bookstore & Stationery", "subsector_pet_shop_and_supplies": "Pet Shop & Supplies", "subsector_florist_plant_shop": "Florist & Plant Shop", "subsector_gift_and_souvenir_shop": "Gift & Souvenir Shop", "subsector_other_retail_and_commerce": "Other (Retail & Commerce)", "subsector_hair_salon": "Hair Salon", "subsector_barber_shop": "Barber Shop", "subsector_nail_studio": "Nail Studio", "subsector_eyelash_and_eyebrow_service": "Eyelash & Eyebrow Service", "subsector_spa_and_massage_center": "Spa & Massage Center", "subsector_skincare_and_beauty_clinic": "Skincare & Beauty Clinic", "subsector_gym_and_fitness_center": "Gym & Fitness Center", "subsector_yoga_and_pilates_studio": "Yoga & Pilates Studio", "subsector_tattoo_and_piercing_studio": "Tattoo & Piercing Studio", "subsector_therapy_services": "Therapy Services", "subsector_other_beauty_and_wellness": "Other (Beauty & Wellness)", "subsector_delivery_service": "Delivery Service", "subsector_car_taxi": "Car Taxi", "subsector_vehicle_rental_service_car_motorbike_bicycle": "Vehicle Rental Service", "subsector_logistics_freight_forwarding_customs_brokerage": "Logistics, Freight Forwarding & Customs Brokerage", "subsector_airport": "Airport", "subsector_port_transport_hub_operations": "Port & Transport Hub Operations", "subsector_boat_ferry": "Boat & Ferry Services", "subsector_vehicle_repair_maintenance_garage": "Vehicle Repair & Maintenance Garage", "subsector_motorbike_parts_accessories_shop": "Motorbike Parts & Accessories Shop", "subsector_car_parts_accessories_store": "Car Parts & Accessories", "subsector_vehicle_wash_detailing_service": "Vehicle Wash & Detailing Service", "subsector_parking_lot_garage_service": "Parking Lot & Garage Service", "subsector_driving_school_license_service": "Driving School & License Service", "subsector_other_transport_logistics": "Other (Transport & Logistics)", "subsector_bank_commercial_specialized": "Bank (Commercial, Specialized)", "subsector_microfinance_institution_mfi": "Microfinance Institution (MFI)", "subsector_money_transfer_payment_e_wallet_agent": "Money Transfer, Payment & E-Wallet Agent", "subsector_pawn_shop_collateral_loan": "Pawn Shop & Collateral Loan Service", "subsector_insurance_provider_broker_agent": "Insurance Provider, Broker & Agent", "subsector_accounting_auditing_tax_advisory_service": "Accounting, Auditing & Tax Advisory Service", "subsector_legal_service_law_firm_notary": "Legal Service, Law Firm & Notary Public", "subsector_investment_securities_firm": "Investment & Securities Firm", "subsector_financial_consultancy_advisory": "Financial Consultancy & Advisory", "subsector_debt_collection_agency": "Debt Collection Agency", "subsector_real_estate_appraisal_valuation": "Real Estate Appraisal & Valuation Service", "subsector_other_finance_and_legal": "Other (Finance & Legal)", "subsector_software_development_custom_solutions": "Software Development & Custom Solutions", "subsector_web_design_development_hosting_agency": "Web Design, Development & Hosting Agency", "subsector_mobile_app_development_ios_android": "Mobile App Development (iOS, Android)", "subsector_digital_marketing_seo_sem_smm_agency": "Digital Marketing (SEO, SEM, SMM) Agency", "subsector_it_consulting_systems_integration": "IT Consulting & Systems Integration", "subsector_cybersecurity_data_protection_services": "Cybersecurity & Data Protection Services", "subsector_internet_service_provider_isp_broadband": "Internet Service Provider (ISP) & Broadband", "subsector_data_center_cloud_computing_iaas_paas_saas": "Data Center & Cloud Computing (IaaS, PaaS, SaaS)", "subsector_it_support_managed_services_outsourcing": "IT Support, Managed Services & Outsourcing", "subsector_telecommunication_services_voip_pabx": "Telecommunication Services (Mobile Operators, VoIP, PABX)", "subsector_computer_hardware_sales_networking_solutions_cctv": "Computer Hardware Sales, Networking Solutions & CCTV", "subsector_ecommerce_solutions_platform_provider": "E-commerce Solutions & Platform Provider", "subsector_fintech_payment_gateway_solutions": "FinTech & Payment Gateway Solutions", "subsector_blockchain_technology_services": "Blockchain Technology Services", "subsector_other_ict_digital_services": "Other (ICT & Digital Services)", "subsector_garment_textile_apparel_factory": "Garment, Textile & Apparel Factory", "subsector_footwear_travel_goods_factory": "Footwear & Travel Goods Factory", "subsector_food_beverage_processing_plant": "Food & Beverage Processing Plant", "subsector_wood_processing_furniture_manufacturing": "Wood Processing & Furniture Manufacturing", "subsector_rubber_plastic_products_manufacturing_packaging": "Rubber & Plastic Products Manufacturing, Packaging", "subsector_light_heavy_engineering_workshop_metal_fabrication": "Light & Heavy Engineering Workshop & Metal Fabrication", "subsector_construction_materials_manufacturing_cement_bricks": "Construction Materials Manufacturing (Cement, Bricks)", "subsector_pharmaceutical_chemical_manufacturing": "Pharmaceutical & Chemical Manufacturing", "subsector_printing_publishing_industrial": "Printing & Publishing (Industrial Scale)", "subsector_other_manufacturing_industrial": "Other (Manufacturing & Industrial)", "subsector_restaurant": "Restaurant", "subsector_cafe_and_coffee_shop": "Cafe & Coffee Shop", "subsector_bubble_tea_and_drink_stall": "Bubble Tea", "subsector_street_food_vendor": "Street Food Vendor", "subsector_bakery_and_pastry_shop": "Bakery & Pastry Shop", "subsector_dessert_shop": "Dessert Shop", "subsector_pub_bar_lounge": "Club, Pub, Bar & Lounge", "subsector_catering_service_food_beverage": "Catering Service (Food & Beverage)", "subsector_food_delivery_platform": "Food Delivery Platform", "subsector_restaurants_offering_delivery": "Restaurants Offering Delivery", "subsector_wholesale_food_and_beverage_supplier": "Wholesale Food & Beverage Supplier", "subsector_ice_cream_gelato_shop": "Ice Cream & Gelato Shop", "subsector_other_food_and_beverage": "Other (Food & Beverage)", "subsector_hotel": "Hotel", "subsector_guesthouse": "Guesthouse", "subsector_resort": "Resort", "subsector_serviced_apartment": "Serviced Apartment", "subsector_short_term_rental_airbnb_homestay": "Short-term Rental (Airbnb, Homestay)", "subsector_travel_agency_and_tour_operator": "Travel Agency & Tour Operator", "subsector_tour_guide_local_experience_provider": "Tour Guide & Local Experience Provider", "subsector_visa_and_travel_document_assistance_service": "Visa & Travel Document Assistance Service", "subsector_transport_rental_for_tourists": "Transport Rental for Tourists (Bicycle, E-bike, Car with Driver)", "subsector_conference_exhibition_center": "Conference & Exhibition Center", "subsector_other_tourism_and_hospitality": "Other (Tourism & Hospitality)", "subsector_real_estate_agent_broker": "Real Estate Agent & Broker", "subsector_property_developer_borey_condo": "Property Developer (Borey, Condo)", "subsector_rental_and_property_management": "Rental & Property Management", "subsector_construction_company_contractor": "Construction Company & Contractor", "subsector_architectural_and_engineering_services": "Architectural & Engineering Services", "subsector_interior_design_service": "Interior Design Service", "subsector_construction_material_supplier": "Construction Material Supplier", "subsector_home_renovation_and_improvement": "Home Renovation & Improvement", "subsector_landscaping_services": "Landscaping Services", "subsector_surveying_services": "Surveying Services", "subsector_other_real_estate_and_construction": "Other (Real Estate & Construction)", "subsector_hospital": "Hospital", "subsector_general_clinic_polyclinic": "General Clinic & Polyclinic", "subsector_dental_clinic_orthodontics": "Dental Clinic & Orthodontics", "subsector_maternity_pediatric_women_health_clinic": "Maternity, Pediatric & Women's Health Clinic", "subsector_specialized_medical_clinic": "Specialized Medical Clinic (Cardiology, Dermatology, etc.)", "subsector_pharmacy_drugstore": "Pharmacy & Drugstore", "subsector_optical_shop_optometrist_ophthalmologist": "Optical Shop, Optometrist & Ophthalmologist", "subsector_diagnostic_lab_and_imaging_center": "Diagnostic Lab & Imaging Center (X-Ray, MRI)", "subsector_traditional_medicine_practitioner_shop": "Traditional Khmer Medicine Practitioner & Shop", "subsector_health_and_wellness_supplement_store": "Health & Wellness Supplement Store", "subsector_physiotherapy_and_rehabilitation_center": "Physiotherapy & Rehabilitation Center", "subsector_home_healthcare_services": "Home Healthcare Services", "subsector_emergency_medical_services_ambulance": "Emergency Medical Services & Ambulance", "subsector_other_medical_and_health": "Other (Medical & Health)", "subsector_kindergarten_and_preschool": "Kindergarten & Preschool", "subsector_international_private_school_k12": "International & Private School (K-12)", "subsector_public_school": "Public School", "subsector_university_and_higher_education": "University & Higher Education Institute", "subsector_language_school": "Language School", "subsector_vocational_and_skill_training_center": "Vocational & Skill Training Center", "subsector_tutoring_and_test_preparation_service": "Tutoring & Test Preparation Service", "subsector_online_course_edtech_platform": "Online Course & EdTech Platform", "subsector_music_art_dance_school": "Music, Art & Dance School", "subsector_sports_coaching_training": "Sports Coaching & Training", "subsector_other_education_and_training": "Other (Education & Training)", "subsector_cinema_movie_theater_multiplex": "Cinema, Movie Theater & Multiplex", "subsector_ktv_karaoke_video_game_lounge": "KTV, Karaoke & Video Game Lounge", "subsector_gaming_cafe_esports_arena_internet_cafe": "Gaming Café, eSports Arena & Internet Café", "subsector_live_music_venue_concert_hall_club_bar_with_live_music": "Live Music Venue, Concert Hall, Club/Bar (with live music)", "subsector_event_management_production_services_rental": "Event Management, Production Services & Equipment Rental", "subsector_media_publishing_news_agency_print_online_magazine": "Media Publishing (News Agency, Print, Online, Magazine)", "subsector_broadcasting_radio_station_television_channel": "Broadcasting (Radio Station, Television Channel)", "subsector_content_creator_influencer_blogger_vlogger_streamer": "Content Creator (Influence<PERSON>, Blogger, Vlogger, Streamer)", "subsector_talent_artist_management_booking_agency": "Talent & Artist Management & Booking Agency", "subsector_amusement_theme_park_water_park_recreation_center": "Amusement, Theme Park, Water Park & Recreation Center", "subsector_art_gallery_museum_cultural_center": "Art Gallery, Museum & Cultural Center", "subsector_photography_videography_services_studios_for_media": "Photography & Videography Services & Studios (for Media/Events)", "subsector_animation_vfx_post_production_studio": "Animation, VFX & Post-production Studio", "subsector_sports_entertainment_arena_stadium": "Sports Entertainment, Arena & Stadium", "subsector_other_entertainment_media": "Other (Entertainment & Media)", "subsector_printing_copying_binding_services": "Printing, Copying & Binding Services", "subsector_personal_photography_studio_portrait_family": "Personal Photography Studio (Portrait, Family)", "subsector_event_planning_coordination_personal_corporate": "Event Planning & Coordination (Personal, Corporate)", "subsector_decoration_services_event_home_office": "Decoration Services (Event, Home, Office)", "subsector_cleaning_services_residential_commercial_industrial": "Cleaning Services (Residential, Commercial, Industrial)", "subsector_laundry_dry_cleaning_ironing_service": "Laundry, Dry Cleaning & Ironing Service", "subsector_repair_services_electronics_consumer": "Repair Services (Consumer Electronics)", "subsector_repair_services_home_appliances_ac_refrigerator": "Repair Services (Home Appliances - AC, Refrigerator)", "subsector_plumbing_sanitary_services": "Plumbing & Sanitary Services", "subsector_electrical_wiring_repair_services": "Electrical Wiring & Repair Services", "subsector_general_handyman_maintenance_services": "General Handyman & Maintenance Services", "subsector_translation_interpretation_localization_services": "Translation, Interpretation & Localization Services", "subsector_business_consulting_management_advisory_non_it": "Business Consulting & Management Advisory (Non-IT)", "subsector_human_resources_recruitment_payroll_services": "Human Resources, Recruitment & Payroll Services", "subsector_security_services_guard_patrol_systems": "Security Services (Guard, Patrol, Systems)", "subsector_market_research_survey_services": "Market Research & Survey Services", "subsector_secretarial_administrative_support_services": "Secretarial & Administrative Support Services", "subsector_waste_management_recycling_services": "Waste Management & Recycling Services", "subsector_pest_control_services": "Pest Control Services", "subsector_moving_relocation_services_packer_mover": "Moving & Relocation Services (Packer & Mover)", "subsector_other_professional_general_services": "Other (Professional & General Services)", "subsector_rice_mill_processor": "Rice Mill & Processor", "subsector_crop_farm_plantation_horticulture": "Crop Farm, Plantation & Horticulture", "subsector_livestock_poultry_dairy_farm": "Livestock, Poultry & Dairy Farm", "subsector_fisheries_aquaculture_hatchery": "Fisheries, Aquaculture & Hatchery", "subsector_agro_processing_food_production_sme": "Agro-processing & Food Production SME", "subsector_agri_input_dealer_fertilizer_seeds_pesticides": "Agri-input Dealer (Fertilizer, Seeds, Pesticides)", "subsector_agricultural_machinery_equipment_supplier_repair": "Agricultural Machinery & Equipment (Supplier, Repair)", "subsector_organic_produce_farm_supplier": "Organic Produce Farm & Supplier", "subsector_agricultural_cooperative": "Agricultural Cooperative", "subsector_other_agriculture_agribusiness": "Other (Agriculture & Agribusiness)", "subsector_pagoda_wat_buddhist_temple": "Pagoda, Wat (Buddhist Temple)", "subsector_church_christian_ministry": "Church, Christian Ministry", "subsector_mosque_islamic_center_surau": "Mosque, Islamic Center,  Surau", "subsector_other_religious_organization": "Other Religious Organization", "subsector_ngo_non_profit_civil_society_organization": "NGO, Non-Profit, Civil Society Organization", "subsector_social_enterprise_community_business": "Social Enterprise & Community Business", "subsector_community_center_library_public_space": "Community Center, Library & Public Space", "subsector_charitable_organization_foundation": "Charitable Organization & Foundation", "subsector_environmental_conservation_group": "Environmental & Conservation Group", "subsector_other_community_and_religious": "Other (Community & Religious)", "subsector_other_not_listed_elsewhere": "Other (Not Listed Elsewhere)"}