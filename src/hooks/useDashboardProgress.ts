import { useMemo } from 'react'

interface UsageData {
  usage_used: number
  usage_limit: number
  cmt_used: number
  cmt_limit: number
}

interface ProgressHookProps {
  usageData: UsageData
  isDashboardLoading: boolean
  isPageDataLoading?: boolean
}

/**
 * Unified hook for dashboard progress bar calculations
 * Ensures consistent animation behavior across all dashboard pages
 */
export function useDashboardProgress({ 
  usageData, 
  isDashboardLoading, 
  isPageDataLoading = false 
}: ProgressHookProps) {
  
  // Message usage percentage with animation trigger
  const messageUsagePercentage = useMemo(() => {
    // Return 0 during loading to trigger left-to-right animation
    if (isDashboardLoading || isPageDataLoading) {
      return 0;
    }
    return Math.min((usageData.usage_used / usageData.usage_limit) * 100, 100);
  }, [usageData.usage_used, usageData.usage_limit, isDashboardLoading, isPageDataLoading]);

  // Comment usage percentage
  const commentUsagePercentage = useMemo(() => {
    if (isDashboardLoading || isPageDataLoading) {
      return 0;
    }
    return Math.min((usageData.cmt_used / usageData.cmt_limit) * 100, 100);
  }, [usageData.cmt_used, usageData.cmt_limit, isDashboardLoading, isPageDataLoading]);

  // Usage limit check
  const isUsageLimitReached = useMemo(() => {
    return usageData.usage_used >= usageData.usage_limit;
  }, [usageData.usage_used, usageData.usage_limit]);

  return {
    messageUsagePercentage,
    commentUsagePercentage,
    isUsageLimitReached,
    messageUsed: usageData.usage_used,
    messageLimit: usageData.usage_limit,
    commentUsed: usageData.cmt_used,
    commentLimit: usageData.cmt_limit
  };
}
