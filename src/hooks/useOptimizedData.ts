'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
// Removed cache imports - using simple sessionStorage for dashboard cache only

export interface UseOptimizedDataOptions {
  enableCache?: boolean
  refetchInterval?: number
  staleTime?: number
}

export interface FaqData {
  id: string
  question: string
  answer: string
  audio_url?: string
  photo_url?: string
  updated_at: string
}

export interface DashboardData {
  clientInfo: {
    username: string
    sector: string | null
    lang: string | null
    plan_type: string | null
    next_billing_date: string | null
    status: string | null
  }
  usageData: {
    usage_used: number
    usage_limit: number
    cmt_used: number
    cmt_limit: number
  }
  knowledgeStats: {
    faqCount: number
    faqLimit: number
    faqUsagePercentage: number
  }
  commentStats: {
    commentUsed: number
    commentLimit: number
    commentUsagePercentage: number
  }
}

// Global request deduplication for API calls
let dashboardRequestPromise: Promise<any> | null = null

// Simple encryption/decryption for session storage
const ENCRYPTION_KEY = 'chhlat_dashboard_2025' 

const simpleEncrypt = (text: string): string => {
  try {
    // Simple XOR encryption with base64 encoding for obfuscation
    const key = ENCRYPTION_KEY
    let encrypted = ''

    for (let i = 0; i < text.length; i++) {
      const keyChar = key.charCodeAt(i % key.length)
      const textChar = text.charCodeAt(i)
      encrypted += String.fromCharCode(textChar ^ keyChar)
    }

    // Base64 encode the result to make it unreadable
    return btoa(encrypted)
  } catch (error) {
    console.warn('Encryption failed, storing as plain text:', error)
    return text
  }
}

const simpleDecrypt = (encryptedText: string): string => {
  try {
    // Decode base64 first
    const decoded = atob(encryptedText)
    const key = ENCRYPTION_KEY
    let decrypted = ''

    for (let i = 0; i < decoded.length; i++) {
      const keyChar = key.charCodeAt(i % key.length)
      const encryptedChar = decoded.charCodeAt(i)
      decrypted += String.fromCharCode(encryptedChar ^ keyChar)
    }

    return decrypted
  } catch (error) {
    console.warn('Decryption failed:', error)
    return encryptedText
  }
}

// Dashboard cache functions removed - now using server-side caching

/**
 * Trigger dashboard refresh after FAQ/photo changes
 * Call this after adding/deleting FAQs or photos to refresh dashboard data
 */
export const triggerDashboardRefresh = (): void => {
  if (typeof window === 'undefined') return
  
  // Trigger dashboard refresh event
  window.dispatchEvent(new CustomEvent('dashboardRefresh'))
}

/**
 * Hook for dashboard data fetching with server-side caching
 */
export function useDashboardData(options: UseOptimizedDataOptions = {}) {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // If there's already a request in progress, wait for it instead of making a new one
      if (dashboardRequestPromise) {
        const result = await dashboardRequestPromise
        setData(result)
        return
      }

      // Use unified API route with dashboard cache
      dashboardRequestPromise = fetch('/api/user/data?cache=dashboard', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const responseData = await response.json()
        if (!responseData.success) {
          throw new Error(responseData.error_msg || 'Failed to fetch dashboard data')
        }
        
        // Return dashboard data (no knowledge stats)
        return responseData.body
      })

      const result = await dashboardRequestPromise
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
      // Clear the request promise after completion (success or failure)
      dashboardRequestPromise = null
    }
  }, [])

  // Fetch data on mount
  useEffect(() => {
    fetchData()
  }, []) // Empty dependency array - run only once on mount

  // Listen for manual refresh events
  useEffect(() => {
    const handleRefresh = () => {
      fetchData()
    }

    // Add event listener for manual refresh
    window.addEventListener('dashboardRefresh', handleRefresh)

    // Cleanup
    return () => {
      window.removeEventListener('dashboardRefresh', handleRefresh)
    }
  }, []) // Empty dependency array - stable event listener

  return { 
    data, 
    loading, 
    error, 
    refetch: fetchData
  }
}

// useKnowledgeData hook removed - use useDashboardData() instead



// Removed useFaqData - replaced by direct Supabase calls in knowledge pages

// Removed usePhotoData - replaced by direct Supabase calls in knowledge pages

// Removed useFaqMutations - replaced by direct Supabase calls in knowledge pages

// Removed usePhotoMutations - replaced by direct Supabase calls in knowledge pages


// usePhotosData removed - photos no longer used

export const clearAllCaches = (): void => {
  if (typeof window === 'undefined') return

  try {
    // All caches are now server-side, no client-side cache to clear
  } catch (error) {
    console.warn('Error clearing caches:', error)
  }
}

// Legacy cache functions removed - all caching is now server-side
