'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { FaChevronLeft, FaChevronRight, FaTelegram, FaStore, FaUtensils, FaHeartbeat, FaPlane, FaCar, FaHome, FaSpa, FaTruck, FaDollarSign, FaLaptopCode, FaIndustry, FaGraduationCap, FaFilm, FaCog, FaSeedling, FaUsers, FaGlobe } from 'react-icons/fa'
import { nanoid } from 'nanoid'
import { useLanguage } from '@/context/LanguageContext'
import LanguageSwitcher from '@/components/LanguageSwitcher'

// Define the sector categories and their options
const sectorCategories = {
  'sector_retail_and_commerce': [
    'subsector_cosmetics_and_skincare_shop',
    'subsector_clothing_and_fashion_store',
    'subsector_electronics_and_phone_shop',
    'subsector_convenience_store_minimart',
    'subsector_grocery_store_market',
    'subsector_home_appliances_store',
    'subsector_furniture_store',
    'subsector_jewelry_and_accessories_shop',
    'subsector_baby_and_kids_store',
    'subsector_sports_equipment_store',
    'subsector_bookstore_and_stationery',
    'subsector_pet_shop_and_supplies',
    'subsector_florist_plant_shop',
    'subsector_gift_and_souvenir_shop',
    'subsector_other_retail_and_commerce'
  ],
  'sector_beauty_and_wellness': [
    'subsector_hair_salon',
    'subsector_barber_shop',
    'subsector_nail_studio',
    'subsector_eyelash_and_eyebrow_service',
    'subsector_spa_and_massage_center',
    'subsector_skincare_and_beauty_clinic',
    'subsector_gym_and_fitness_center',
    'subsector_yoga_and_pilates_studio',
    'subsector_tattoo_and_piercing_studio',
    'subsector_therapy_services',
    'subsector_other_beauty_and_wellness'
  ],
  'sector_transport_and_logistics': [
    'subsector_delivery_service',
    'subsector_car_taxi',
    'subsector_vehicle_rental_service_car_motorbike_bicycle',
    'subsector_logistics_freight_forwarding_customs_brokerage',
    'subsector_airport',
    'subsector_port_transport_hub_operations',
    'subsector_boat_ferry',
    'subsector_vehicle_repair_maintenance_garage',
    'subsector_motorbike_parts_accessories_shop',
    'subsector_car_parts_accessories_store',
    'subsector_vehicle_wash_detailing_service',
    'subsector_parking_lot_garage_service',
    'subsector_driving_school_license_service',
    'subsector_other_transport_logistics'
  ],
  'sector_finance_and_legal': [
    'subsector_bank_commercial_specialized',
    'subsector_microfinance_institution_mfi',
    'subsector_money_transfer_payment_e_wallet_agent',
    'subsector_pawn_shop_collateral_loan',
    'subsector_insurance_provider_broker_agent',
    'subsector_accounting_auditing_tax_advisory_service',
    'subsector_legal_service_law_firm_notary',
    'subsector_investment_securities_firm',
    'subsector_financial_consultancy_advisory',
    'subsector_debt_collection_agency',
    'subsector_real_estate_appraisal_valuation',
    'subsector_other_finance_and_legal'
  ],
  'sector_ict_and_digital_services': [
    'subsector_software_development_custom_solutions',
    'subsector_web_design_development_hosting_agency',
    'subsector_mobile_app_development_ios_android',
    'subsector_digital_marketing_seo_sem_smm_agency',
    'subsector_it_consulting_systems_integration',
    'subsector_cybersecurity_data_protection_services',
    'subsector_internet_service_provider_isp_broadband',
    'subsector_data_center_cloud_computing_iaas_paas_saas',
    'subsector_it_support_managed_services_outsourcing',
    'subsector_telecommunication_services_voip_pabx',
    'subsector_computer_hardware_sales_networking_solutions_cctv',
    'subsector_ecommerce_solutions_platform_provider',
    'subsector_fintech_payment_gateway_solutions',
    'subsector_blockchain_technology_services',
    'subsector_other_ict_digital_services'
  ],
  'sector_manufacturing_and_industrial': [
    'subsector_garment_textile_apparel_factory',
    'subsector_footwear_travel_goods_factory',
    'subsector_food_beverage_processing_plant',
    'subsector_wood_processing_furniture_manufacturing',
    'subsector_rubber_plastic_products_manufacturing_packaging',
    'subsector_light_heavy_engineering_workshop_metal_fabrication',
    'subsector_construction_materials_manufacturing_cement_bricks',
    'subsector_pharmaceutical_chemical_manufacturing',
    'subsector_printing_publishing_industrial',
    'subsector_other_manufacturing_industrial'
  ],
  'sector_food_and_beverage': [
    'subsector_restaurant',
    'subsector_cafe_and_coffee_shop',
    'subsector_bubble_tea_and_drink_stall',
    'subsector_street_food_vendor',
    'subsector_bakery_and_pastry_shop',
    'subsector_dessert_shop',
    'subsector_pub_bar_lounge',
    'subsector_catering_service_food_beverage',
    'subsector_food_delivery_platform',
    'subsector_restaurants_offering_delivery',
    'subsector_wholesale_food_and_beverage_supplier',
    'subsector_ice_cream_gelato_shop',
    'subsector_other_food_and_beverage'
  ],
  'sector_tourism_and_hospitality': [
    'subsector_hotel',
    'subsector_guesthouse',
    'subsector_resort',
    'subsector_serviced_apartment',
    'subsector_short_term_rental_airbnb_homestay',
    'subsector_travel_agency_and_tour_operator',
    'subsector_tour_guide_local_experience_provider',
    'subsector_visa_and_travel_document_assistance_service',
    'subsector_transport_rental_for_tourists',
    'subsector_conference_exhibition_center',
    'subsector_other_tourism_and_hospitality'
  ],
  'sector_real_estate_and_construction': [
    'subsector_real_estate_agent_broker',
    'subsector_property_developer_borey_condo',
    'subsector_rental_and_property_management',
    'subsector_construction_company_contractor',
    'subsector_architectural_and_engineering_services',
    'subsector_interior_design_service',
    'subsector_construction_material_supplier',
    'subsector_home_renovation_and_improvement',
    'subsector_landscaping_services',
    'subsector_surveying_services',
    'subsector_other_real_estate_and_construction'
  ],
  'sector_medical_and_health': [
    'subsector_hospital',
    'subsector_general_clinic_polyclinic',
    'subsector_dental_clinic_orthodontics',
    'subsector_maternity_pediatric_women_health_clinic',
    'subsector_specialized_medical_clinic',
    'subsector_pharmacy_drugstore',
    'subsector_optical_shop_optometrist_ophthalmologist',
    'subsector_diagnostic_lab_and_imaging_center',
    'subsector_traditional_medicine_practitioner_shop',
    'subsector_health_and_wellness_supplement_store',
    'subsector_physiotherapy_and_rehabilitation_center',
    'subsector_home_healthcare_services',
    'subsector_emergency_medical_services_ambulance',
    'subsector_other_medical_and_health'
  ],
  'sector_education_and_training': [
    'subsector_kindergarten_and_preschool',
    'subsector_international_private_school_k12',
    'subsector_public_school',
    'subsector_university_and_higher_education',
    'subsector_language_school',
    'subsector_vocational_and_skill_training_center',
    'subsector_tutoring_and_test_preparation_service',
    'subsector_online_course_edtech_platform',
    'subsector_music_art_dance_school',
    'subsector_sports_coaching_training',
    'subsector_other_education_and_training'
  ],
  'sector_entertainment_and_media': [
    'subsector_cinema_movie_theater_multiplex',
    'subsector_ktv_karaoke_video_game_lounge',
    'subsector_gaming_cafe_esports_arena_internet_cafe',
    'subsector_live_music_venue_concert_hall_club_bar_with_live_music',
    'subsector_event_management_production_services_rental',
    'subsector_media_publishing_news_agency_print_online_magazine',
    'subsector_broadcasting_radio_station_television_channel',
    'subsector_content_creator_influencer_blogger_vlogger_streamer',
    'subsector_talent_artist_management_booking_agency',
    'subsector_amusement_theme_park_water_park_recreation_center',
    'subsector_art_gallery_museum_cultural_center',
    'subsector_photography_videography_services_studios_for_media',
    'subsector_animation_vfx_post_production_studio',
    'subsector_sports_entertainment_arena_stadium',
    'subsector_other_entertainment_media'
  ],
  'sector_services': [
    'subsector_printing_copying_binding_services',
    'subsector_personal_photography_studio_portrait_family',
    'subsector_event_planning_coordination_personal_corporate',
    'subsector_decoration_services_event_home_office',
    'subsector_cleaning_services_residential_commercial_industrial',
    'subsector_laundry_dry_cleaning_ironing_service',
    'subsector_repair_services_electronics_consumer',
    'subsector_repair_services_home_appliances_ac_refrigerator',
    'subsector_plumbing_sanitary_services',
    'subsector_electrical_wiring_repair_services',
    'subsector_general_handyman_maintenance_services',
    'subsector_translation_interpretation_localization_services',
    'subsector_business_consulting_management_advisory_non_it',
    'subsector_human_resources_recruitment_payroll_services',
    'subsector_security_services_guard_patrol_systems',
    'subsector_market_research_survey_services',
    'subsector_secretarial_administrative_support_services',
    'subsector_waste_management_recycling_services',
    'subsector_pest_control_services',
    'subsector_moving_relocation_services_packer_mover',
    'subsector_other_professional_general_services'
  ],
  'sector_agriculture_and_agribusiness': [
    'subsector_rice_mill_processor',
    'subsector_crop_farm_plantation_horticulture',
    'subsector_livestock_poultry_dairy_farm',
    'subsector_fisheries_aquaculture_hatchery',
    'subsector_agro_processing_food_production_sme',
    'subsector_agri_input_dealer_fertilizer_seeds_pesticides',
    'subsector_agricultural_machinery_equipment_supplier_repair',
    'subsector_organic_produce_farm_supplier',
    'subsector_agricultural_cooperative',
    'subsector_other_agriculture_agribusiness'
  ],
  'sector_community_and_religious': [
    'subsector_pagoda_wat_buddhist_temple',
    'subsector_church_christian_ministry',
    'subsector_mosque_islamic_center_surau',
    'subsector_other_religious_organization',
    'subsector_ngo_non_profit_civil_society_organization',
    'subsector_social_enterprise_community_business',
    'subsector_community_center_library_public_space',
    'subsector_charitable_organization_foundation',
    'subsector_environmental_conservation_group',
    'subsector_other_community_and_religious'
  ],
  'sector_others': [
    'subsector_other_not_listed_elsewhere'
  ]
};

// Category icons mapping
const categoryIcons = {
  'sector_retail_and_commerce': <FaStore className="w-6 h-6" />,
  'sector_beauty_and_wellness': <FaSpa className="w-6 h-6" />,
  'sector_transport_and_logistics': <FaTruck className="w-6 h-6" />,
  'sector_finance_and_legal': <FaDollarSign className="w-6 h-6" />,
  'sector_ict_and_digital_services': <FaLaptopCode className="w-6 h-6" />,
  'sector_manufacturing_and_industrial': <FaIndustry className="w-6 h-6" />,
  'sector_food_and_beverage': <FaUtensils className="w-6 h-6" />,
  'sector_tourism_and_hospitality': <FaPlane className="w-6 h-6" />,
  'sector_real_estate_and_construction': <FaHome className="w-6 h-6" />,
  'sector_medical_and_health': <FaHeartbeat className="w-6 h-6" />,
  'sector_education_and_training': <FaGraduationCap className="w-6 h-6" />,
  'sector_entertainment_and_media': <FaFilm className="w-6 h-6" />,
  'sector_services': <FaCog className="w-6 h-6" />,
  'sector_agriculture_and_agribusiness': <FaSeedling className="w-6 h-6" />,
  'sector_community_and_religious': <FaUsers className="w-6 h-6" />,
  'sector_others': <FaGlobe className="w-6 h-6" />,
};

export default function RegisterPage() {
  const { t } = useLanguage()
  const [step, setStep] = useState(1)
  const [plan, setPlan] = useState<string>('')
  const [username, setUsername] = useState('')
  const [selectedLanguage, setSelectedLanguage] = useState<string>('km') // Default to Khmer
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('')
  const [agreeToTerms, setAgreeToTerms] = useState<boolean>(true) // Auto-ticked by default
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isCheckingUsername, setIsCheckingUsername] = useState(false)
  const [registrationCode, setRegistrationCode] = useState<string>('')
  const [isCopied, setIsCopied] = useState(false)
  const [showCodeModal, setShowCodeModal] = useState(false)
  const [countdown, setCountdown] = useState<number>(15 * 60) // 15 minutes in seconds
  const logoRef = useRef<HTMLDivElement>(null)

  // Language options (same as dashboard settings)
  const languageOptions = [
    { code: 'km', name: 'Khmer', nativeName: 'ខ្មែរ' },
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'zh', name: 'Chinese', nativeName: '中文' },
    // { code: 'th', name: 'Thai', nativeName: 'ไทย' },
    { code: 'vi', name: 'Vietnamese', nativeName: 'Tiếng Việt' },
    { code: 'ko', name: 'Korean', nativeName: '한국어' },
    { code: 'id', name: 'Indonesian', nativeName: 'Bahasa Indonesia' },
    { code: 'fr', name: 'French', nativeName: 'Français' },
    { code: 'ja', name: 'Japanese', nativeName: '日本語' }
  ]

  // Check for URL parameters on component mount
  useEffect(() => {
    // Get URL parameters
    const params = new URLSearchParams(window.location.search)
    const planParam = params.get('plan')

    // If plan parameter exists, set it and move to step 2
    if (planParam) {
      // Capitalize first letter to ensure consistent format (Basic, Pro, or Premium)
      const normalizedPlan = planParam.charAt(0).toUpperCase() + planParam.slice(1).toLowerCase()
      setPlan(normalizedPlan)
      setStep(2)
    }
  }, [])

  // Handle next step
  const handleNext = async () => {
    if (step === 2) {
      if (!username.trim()) {
        setError('Please enter a username')
        return
      }

      // Validate username format (alphanumeric and underscores only)
      const usernameRegex = /^[a-zA-Z0-9_]+$/
      if (!usernameRegex.test(username.trim())) {
        setError('Username can only contain letters, numbers, and underscores')
        return
      }

      // Check if username already exists
      setError(null)
      setIsCheckingUsername(true)

      try {
        const response = await fetch('/api/register/check-username', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username: username.trim() }),
        })

        const data = await response.json()

        if (data.exists) {
          setError('This username is already taken. Please choose another one.')
          setIsCheckingUsername(false)
          return
        }

        // If there's a warning, show it but allow the user to proceed
        if (data.warning) {
          // Show warning but don't prevent proceeding
          setError(`Note: ${data.warning}`)
        }
      } catch (err) {
        console.error('Error checking username:', err)
        setError('Failed to check username availability. Please try again.')
        setIsCheckingUsername(false)
        return
      }

      setIsCheckingUsername(false)
    }

    // Step 3 is now language selection - no validation needed, just proceed
    if (step === 3) {
      // Language is already selected (default or user choice), just proceed
    }

    if (step === 4 && (!selectedCategory || !selectedSubcategory)) {
      setError('Please select both a category and subcategory')
      return
    }

    if (step === 4 && !agreeToTerms) {
      setError('Please agree to the terms and conditions to continue')
      return
    }


    setError(null)
    setStep(step + 1)
  }

  // Handle previous step
  const handlePrevious = () => {
    setError(null)

    // If on step 4 and a category is selected, go back to category selection
    if (step === 4 && selectedCategory) {
      setSelectedCategory('')
      setSelectedSubcategory('')
    } else {
      // Otherwise, go to previous step
      setStep(step - 1)
    }
  }

  // Helper function to get the correct sector text for API
  const getSectorTextForAPI = (subcategory: string, category: string): string => {
    // Special case for "Others" sector
    if (subcategory === 'subsector_other_not_listed_elsewhere') {
      return 'General Business'
    }
    
    // For "Other (...)" subsectors, return the main sector name
    const subcategoryText = t(subcategory)
    if (subcategoryText.startsWith('Other (') && subcategoryText.endsWith(')')) {
      return t(category) // Return main sector name
    }
    
    // For all other cases, return the normal subsector translation
    return subcategoryText
  }

  // Handle category selection
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    
    // Special handling for "Others" sector - auto-select the subsector
    if (category === 'sector_others') {
      setSelectedSubcategory('subsector_other_not_listed_elsewhere')
    } else {
      setSelectedSubcategory('')
    }
  }

  // Handle registration
  const handleRegister = async () => {
    // Validate subcategory selection
    if (!selectedSubcategory) {
      setError('Please select a subcategory')
      return
    }

    // Validate terms agreement
    if (!agreeToTerms) {
      setError('Please agree to the terms and conditions to continue')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Generate a unique registration code
      const code = nanoid(16)
      setRegistrationCode(code)

      // Send registration data to webhook instead of Redis
      const webhookPayload = {
        code,
        plan,
        username: username.trim(),
        sector: getSectorTextForAPI(selectedSubcategory, selectedCategory), // Use helper function for correct sector text
        lang: selectedLanguage,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        ttl: 900, // 15 minutes in seconds
        timestamp: Date.now(),
        action: 'register'
      }

      const response = await fetch('/api/register/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookPayload),
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Failed to store registration data')
      }

      // Reset countdown and show the registration code modal
      setCountdown(15 * 60) // Reset to 15 minutes
      setShowCodeModal(true)
    } catch (err: any) {
      setError(err.message || 'An error occurred during registration')
    } finally {
      setIsLoading(false)
    }
  }

  // Format time from seconds to MM:SS
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // Start countdown when code modal is shown
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (showCodeModal && countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prevCountdown => {
          if (prevCountdown <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prevCountdown - 1;
        });
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [showCodeModal, countdown]);

  // Handle copy code to clipboard
  const handleCopyCode = () => {
    if (registrationCode) {
      navigator.clipboard.writeText(registrationCode)
        .then(() => {
          setIsCopied(true)
          setTimeout(() => setIsCopied(false), 2000)
        })
        .catch(err => {
          console.error('Failed to copy code:', err)
        })
    }
  }
  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative">
      {/* Language Switcher */}
      <div className="absolute top-4 right-4 z-[70]">
        <LanguageSwitcher />
      </div>

      <div ref={logoRef} className="px-4 py-8 mx-auto flex items-center justify-center relative z-[60]">
        <Link href="/">
          <img
            src="/images/white_tran_logo.svg"
            alt="ChhlatBot"
            className="h-10 w-auto"
          />
        </Link>
      </div>

      {/* Registration Form - hide when modal is shown */}
      {!showCodeModal && (
        <div className="px-4 pb-8 mt-8">
          <motion.div
            className="w-full max-w-md mx-auto p-4 sm:p-6 md:p-8 rounded-xl shadow-xl"
            style={{
              border: '1px solid rgba(134, 107, 255, 0.5)',
              backgroundColor: 'rgba(255, 255, 255, 0.025)',
              boxShadow: '0 0 15px rgba(134, 107, 255, 0.5)'
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
          {/* Hide title, navigation and all form content when registration modal is shown */}
          {!showCodeModal && (
            <>
              <h1 className="text-xl sm:text-2xl md:text-3xl mt-2 font-bold mb-8 sm:mb-8 md:mb-10 font-title text-center">
                {t('register_title')}
              </h1>

              {/* Navigation buttons - show at top for better mobile UX */}
              {step > 1 && (
                <div className="mb-4 sm:mb-6 md:mb-8 flex justify-between">
                  <button
                    type="button"
                    onClick={handlePrevious}
                    className="flex items-center px-4 py-2 bg-black/30 md:hover:bg-black/50 md:hover:border-white/50 border border-white/20 text-white font-medium rounded-lg transition-colors"
                    style={{
                      boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                    }}
                  >
                    <FaChevronLeft className="mr-2" /> {t('register_back')}
                  </button>

                  {step < 4 ? (
                    <button
                      type="button"
                      onClick={handleNext}
                      disabled={isCheckingUsername}
                      className="flex items-center px-4 py-2 bg-jade-purple md:hover:bg-jade-purple-dark text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-jade-purple/50"
                      style={{
                        boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                      }}
                    >
                      {step === 2 && isCheckingUsername ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                          {t('register_checking')}
                        </>
                      ) : (
                        <>
                          {t('register_next')} <FaChevronRight className="ml-2" />
                        </>
                      )}
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={handleRegister}
                      disabled={isLoading}
                      className="flex items-center px-4 py-2 bg-jade-purple md:hover:bg-jade-purple-dark text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-jade-purple/50"
                      style={{
                        boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                      }}
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                          {t('register_registering')}
                        </>
                      ) : (
                        t('register_register_button')
                      )}
                    </button>
                  )}
                </div>
              )}

              {/* Error message - moved here for better UX */}
              {error && (
                <div className="p-3 mt-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-500 text-sm">
                  {error}
                </div>
              )}
            </>
          )}

          {/* Form steps - hide when registration modal is shown */}
          {!showCodeModal && (
            <>
              <AnimatePresence mode="wait">
                {step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-3 sm:space-y-4 md:space-y-6"
              >
                <div className="text-center mb-2 sm:mb-3 md:mb-4 text-zinc-300">
                  <p className="text-sm sm:text-base">{t('register_select_plan')}</p>
                  <p className="text-xs sm:text-sm text-zinc-500 mt-0.5 sm:mt-1">({t('register_free_trial')})</p>
                </div>

                <div className="space-y-2 sm:space-y-3 md:space-y-4">
                  <button
                    type="button"
                    onClick={() => {
                      setPlan('Basic')
                      setError(null)
                      setStep(2)
                    }}
                    className={`w-full p-3 sm:p-4 rounded-lg border transition-colors ${
                      plan === 'Basic' || plan === 'basic'
                        ? 'border-jade-purple-dark bg-jade-purple-dark/20 text-white'
                        : 'border-white/25 bg-white/5 md:hover:bg-jade-purple/10 md:hover:border-jade-purple/50'
                    }`}
                  >
                    <div className="font-extrabold text-xl sm:text-2xl mb-2 sm:mb-3">{t('plan_basic')}</div>
                    <div className="text-center space-y-0.5 sm:space-y-1 text-xs sm:text-sm py-1 sm:py-2 px-2 sm:px-3 rounded-lg mt-1 sm:mt-2">
                      <div>{t('register_basic_messages')}</div>
                      <div>{t('register_basic_channels')}</div>
                      <div>{t('register_basic_support')}</div>
                    </div>
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      setPlan('Pro')
                      setError(null)
                      setStep(2)
                    }}
                    className={`w-full p-3 sm:p-4 rounded-lg border transition-colors ${
                      plan === 'Pro' || plan === 'pro'
                        ? 'border-jade-purple-dark bg-jade-purple-dark/20 text-white'
                        : 'border-white/25 bg-white/5 md:hover:bg-jade-purple/10 md:hover:border-jade-purple/50'
                    }`}
                  >
                    <div className="font-extrabold text-xl sm:text-2xl mb-2 sm:mb-3">{t('plan_pro')}</div>
                    <div className="text-center space-y-0.5 sm:space-y-1 text-xs sm:text-sm py-1 sm:py-2 px-2 sm:px-3 rounded-lg mt-1 sm:mt-2">
                      <div>{t('register_pro_messages')}</div>
                      <div>{t('register_pro_channels')}</div>
                      <div>{t('register_pro_support')}</div>
                    </div>
                  </button>
                </div>
              </motion.div>
            )}

            {step === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="text-center mb-4 text-zinc-300">
                  <p>{t('register_choose_username')}</p>
                  <p className="text-sm text-zinc-500 mt-1">({t('register_username_description')})</p>
                </div>

                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-zinc-300 mb-2">
                    {t('register_username_label')}
                  </label>
                  <div className="relative">
                    <input
                      id="username"
                      type="text"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && username.trim()) {
                          e.preventDefault();
                          handleNext();
                        }
                      }}
                      className="w-full px-4 py-3 rounded-lg bg-black/30 border border-white/20 text-white placeholder-zinc-500 focus:outline-none focus:border-white/40"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                      placeholder={t('register_username_placeholder')}
                      required
                      disabled={isCheckingUsername}
                    />
                    {isCheckingUsername && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-jade-purple"></div>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-zinc-500 mt-1">
                    {t('register_username_rules')}
                  </p>
                </div>
              </motion.div>
            )}

            {step === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="text-center mb-4 text-zinc-300">
                  <p className="text-sm sm:text-base font-medium">{t('register_select_language')}</p>
                  <div className="text-xs sm:text-sm text-zinc-400 mt-3 text-left space-y-2">
                    <p className="font-medium text-zinc-300">{t('register_language_main_purpose')}</p>
                    <ul className="space-y-1 ml-4">
                      <li className="flex items-start">
                        <span className="text-jade-purple mr-2 mt-0.5">•</span>
                        <span>{t('register_language_point1')}</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-jade-purple mr-2 mt-0.5">•</span>
                        <span>{t('register_language_point2')}</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-jade-purple mr-2 mt-0.5">•</span>
                        <span>{t('register_language_point3')}</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-jade-purple mr-2 mt-0.5">•</span>
                        <span>{t('register_language_point4')}</span>
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-3">
                  {languageOptions.map((language) => (
                    <button
                      key={language.code}
                      type="button"
                      onClick={() => setSelectedLanguage(language.code)}
                      className={`w-full flex items-center justify-between px-4 py-3 rounded-lg border transition-colors ${
                        selectedLanguage === language.code
                          ? 'border-jade-purple-dark bg-jade-purple-dark/20 text-white'
                          : 'border-white/25 bg-white/5 md:hover:bg-jade-purple/10 md:hover:border-jade-purple/50'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                          selectedLanguage === language.code ? 'bg-jade-purple text-white' : 'bg-white/10 text-zinc-400'
                        }`}>
                          {language.code === 'km' ? (
                            <span className="text-xs font-semibold">ខ្មែរ</span>
                          ) : (
                            <span className="text-xs font-semibold">{language.code.toUpperCase()}</span>
                          )}
                        </div>
                        <div className="text-left">
                          <div className="text-sm font-medium">{language.name}</div>
                          <div className="text-xs text-zinc-400">{language.nativeName}</div>
                        </div>
                      </div>
                      {selectedLanguage === language.code && (
                        <div className="text-jade-purple">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </button>
                  ))}
                </div>

              </motion.div>
            )}

            {step === 4 && (
              <motion.div
                key="step4"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-3 sm:space-y-4 md:space-y-6"
              >
                <div className="text-center mb-2 sm:mb-3 md:mb-4 text-zinc-300">
                  <p className="text-sm sm:text-base">{t('register_select_sector')}</p>
                  <p className="text-xs sm:text-sm text-zinc-500 mt-0.5 sm:mt-1">({t('register_sector_description')})</p>
                </div>

                {!selectedCategory ? (
                  // Category selection
                  <div className="grid grid-cols-2 gap-2 sm:gap-3">
                    {Object.keys(sectorCategories).map((category) => (
                      <button
                        key={category}
                        type="button"
                        onClick={() => handleCategoryChange(category)}
                        className="p-3 sm:p-4 rounded-lg border border-white/25 bg-white/5 md:hover:bg-jade-purple/10 md:hover:border-jade-purple/50 transition-colors text-left"
                      >
                        <div className="flex items-center space-x-2 sm:space-x-3 mb-1 sm:mb-2">
                          <div className="text-jade-purple flex-shrink-0">
                            {categoryIcons[category as keyof typeof categoryIcons]}
                          </div>
                          <div className="text-white font-medium text-xs sm:text-sm leading-tight">
                            {t(category)}
                          </div>
                        </div>
                        <div className="text-zinc-400 text-xs leading-tight">
                          {sectorCategories[category as keyof typeof sectorCategories].length} {t('register_options')}
                        </div>
                      </button>
                    ))}
                  </div>
                ) : selectedCategory === 'sector_others' ? (
                  // For "Others" sector, show selected category and skip subcategory selection
                  <div className="space-y-3 sm:space-y-4">
                    <div className="flex items-center justify-center space-x-2 text-zinc-300">
                      <div className="text-jade-purple">
                        {categoryIcons[selectedCategory as keyof typeof categoryIcons]}
                      </div>
                      <span className="font-medium text-sm sm:text-base">
                        {t(selectedCategory)}
                      </span>
                    </div>
                    <div className="text-center text-sm text-zinc-400 bg-white/5 rounded-lg p-3 border border-white/10">
                      Selected: General Business
                    </div>
                  </div>
                ) : (
                  // Subcategory selection for other sectors
                  <div className="space-y-3 sm:space-y-4">
                    <div className="flex items-center justify-center space-x-2 text-zinc-300">
                      <div className="text-jade-purple">
                        {categoryIcons[selectedCategory as keyof typeof categoryIcons]}
                      </div>
                      <span className="font-medium text-sm sm:text-base">
                        {t(selectedCategory)}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 gap-2 max-h-64 overflow-y-auto">
                      {sectorCategories[selectedCategory as keyof typeof sectorCategories].map((subcategory) => (
                        <button
                          key={subcategory}
                          type="button"
                          onClick={() => setSelectedSubcategory(subcategory)}
                          className={`p-3 rounded-lg border transition-colors text-left ${
                            selectedSubcategory === subcategory
                              ? 'border-jade-purple-dark bg-jade-purple-dark/20 text-white'
                              : 'border-white/25 bg-white/5 md:hover:bg-jade-purple/10 md:hover:border-jade-purple/50'
                          }`}
                        >
                          <div className="text-sm font-medium">
                            {t(subcategory)}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Terms and Conditions Agreement */}
                {selectedSubcategory && (
                  <div className="mt-6 pt-4 border-t border-white/10">
                    <div className="flex items-start space-x-3">
                      <button
                        type="button"
                        onClick={() => setAgreeToTerms(!agreeToTerms)}
                        className={`flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                          agreeToTerms
                            ? 'bg-jade-purple border-jade-purple text-white'
                            : 'border-white/30 md:hover:border-jade-purple/50'
                        }`}
                      >
                        {agreeToTerms && (
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </button>
                      <div className="text-sm text-zinc-300 leading-relaxed">
                        <span>{t('register_agree_to')} </span>
                        <Link
                          href="/terms"
                          target="_blank"
                          className="text-jade-purple md:hover:text-jade-purple-light underline"
                        >
                          {t('register_terms_and_conditions')}
                        </Link>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Step indicator - moved to bottom for better mobile UX */}
          <div className="flex justify-between mt-4 sm:mt-6 md:mt-8 mb-4 sm:mb-6 md:mb-8">
            {[1, 2, 3, 4].map((stepNumber) => (
              <div
                key={stepNumber}
                className={`w-8 h-8 rounded-full flex items-center justify-center border ${
                  step === stepNumber
                    ? 'bg-jade-purple/20 text-white border-jade-purple'
                    : step > stepNumber
                      ? 'bg-jade-purple/20 text-white border-jade-purple'
                      : 'bg-black/30 text-zinc-400 border-white/20'
                }`}
                style={{
                  boxShadow: step >= stepNumber
                    ? '0 0 10px rgba(134, 107, 255, 0.3)'
                    : 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                }}
              >
                {stepNumber}
              </div>
            ))}
          </div>

          <div className="text-center text-sm sm:text-base md:text-lg text-zinc-500">
            {t('register_have_account')}{' '}
            <Link href="/access" className="text-jade-purple md:hover:text-jade-purple-light font-medium">
              {t('register_login')}
            </Link>
          </div>
            </>
          )}
          </motion.div>
        </div>
      )}

      {/* Registration Code Modal */}
      {showCodeModal && (
        <div className="px-4 pb-8 mt-8">
          <motion.div
            className="w-full max-w-md mx-auto p-4 sm:p-6 md:p-8 rounded-xl shadow-xl"
            style={{
              border: '1px solid rgba(134, 107, 255, 0.5)',
              backgroundColor: 'rgba(255, 255, 255, 0.025)',
              boxShadow: '0 0 15px rgba(134, 107, 255, 0.5)'
            }}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center justify-center mb-4">
              <div className="bg-jade-purple/20 rounded-full p-3">
                <svg className="w-8 h-8 text-jade-purple" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
            </div>
            <div className="text-center mb-4">
              <h3 className="text-xl font-bold text-white font-title">{t('register_modal_title')}</h3>
              <p className="text-sm text-zinc-400 mt-1 font-body">{t('register_modal_subtitle')}</p>
            </div>

            <div className="space-y-4">
              <div>
                <p className="text-sm text-zinc-300 mb-2">{t('register_modal_code_label')}</p>
                <div className="relative">
                  <div className="flex items-center">
                    <div className="w-full bg-black/30 border border-white/20 rounded-lg px-4 pr-12 py-3 font-mono text-white text-center"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}>
                      {registrationCode}
                    </div>
                    <div className="absolute right-4 flex gap-2">
                      <button
                        type="button"
                        onClick={handleCopyCode}
                        className="text-zinc-400 md:hover:text-white"
                        title="Copy to clipboard"
                      >
                        {isCopied ? (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
                {isCopied && (
                  <p className="text-xs text-green-500 mt-1 text-center">{t('register_modal_copied')}</p>
                )}
              </div>

              <div className="text-center">
                <div className="text-sm text-zinc-400">
                  <p>{t('register_modal_expire_text')}</p>
                  <div className="text-jade-purple font-medium text-3xl mb-2">{formatTime(countdown)}</div>
                  <p className="mt-1">{t('register_modal_instruction1')}</p>
                  <br></br>
                  <p className="mt-1">{t('register_modal_instruction2')}</p>
                </div>
              </div>

              <a
                href="tg://resolve?domain=chhlat_notify_bot"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center w-full py-3 px-4 bg-[#0088cc] md:hover:bg-[#0077b5] text-white font-medium rounded-lg transition-colors font-body"
                style={{
                  boxShadow: '0 0 10px rgba(0, 136, 204, 0.3)'
                }}
              >
                <FaTelegram className="mr-2" /> {t('register_modal_telegram')}
              </a>

              <div className="text-center text-sm text-zinc-400 mt-2">
                <p>{t('register_modal_final_instruction')}</p>
              </div>

              <div className="mt-4 text-center">
                <Link
                  href="/access"
                  className="inline-block px-6 py-2 bg-jade-purple md:hover:bg-jade-purple-dark text-white font-medium rounded-lg transition-colors border border-jade-purple/50"
                  style={{
                    boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                  }}
                >
                  {t('register_modal_login')}
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}
