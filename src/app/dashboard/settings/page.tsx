'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button, LinkButton } from '@/components/ui'
import DashboardHeader from '@/components/ui/DashboardHeader'
import LogoutConfirmationModal from '@/components/ui/modals/LogoutConfirmationModal'
import UpdateStatusOverlay from '@/components/ui/knowledge/UpdateStatusOverlay'

export default function SettingsPage() {
  const [oldPassword, setOldPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSigningOut, setIsSigningOut] = useState(false)
  const [showLogoutConfirmation, setShowLogoutConfirmation] = useState(false)
  const [showSuccessPopup, setShowSuccessPopup] = useState(false)

  const router = useRouter()
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Disable page scroll when any popup is open
  useEffect(() => {
    if (showLogoutConfirmation || showSuccessPopup) {
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = ''
      }
    }
  }, [showLogoutConfirmation, showSuccessPopup])

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    if (newPassword !== confirmPassword) {
      setError(t('settings_passwords_no_match'))
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          oldPassword,
          newPassword
        }),
        cache: 'no-store'
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || t('settings_password_update_failed'))
      }

      // Show success popup
      setShowSuccessPopup(true)
      setTimeout(() => {
        setShowSuccessPopup(false)
      }, 1500)

      // Clear form fields
      setOldPassword('')
      setNewPassword('')
      setConfirmPassword('')
    } catch (error: any) {
      setError(error.message || t('settings_password_update_failed'))
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true)

      // Clear server-side cache
      try {
        await fetch('/api/auth/clear-cache', {
          method: 'POST',
          cache: 'no-store'
        })
      } catch (cacheError) {
        console.warn('Failed to clear server cache:', cacheError)
      }

      // Use API endpoint for logout
      await fetch('/api/auth/signout', {
        method: 'POST',
        cache: 'no-store'
      })

      router.push('/access')
      setTimeout(() => {
        router.refresh()
      }, 100)
    } catch (error) {
      console.error('Error signing out:', error)
      router.push('/')
    }
  }

  const confirmSignOut = () => {
    setShowLogoutConfirmation(true)
  }

  return (
    <div className={`min-h-screen ${themeConfig.pageBackground} flex flex-col relative pb-16`}>
      {/* Background effects */}
      {themeConfig.backgroundEffects}
      {theme === 'dark' && (
        <>
          <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
          <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>
        </>
      )}

      {/* Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.card} rounded-2xl px-4 py-3 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <div>
          <DashboardHeader backHref="/dashboard" titleKey="settings" />

          <div className="max-w-md mx-auto">
            {/* Password Change Section */}
            <div
              className={`relative ${themeConfig.card} rounded-2xl p-6 mb-8 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
              style={theme === 'dark' ? {
                boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              } : {
                boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
              }}
            >
              <div className="relative z-10">
                <h2 className={`text-xl font-bold mb-6 font-title ${themeConfig.text}`}>
                  {t('settings_change_password_title')}
                </h2>

                <form onSubmit={handlePasswordChange} className="space-y-4">
                  <div>
                    <label htmlFor="oldPassword" className={`block text-sm font-medium ${themeConfig.textSecondary} font-body mb-1`}>
                      {t('settings_current_password')}
                    </label>
                    <input
                      type="password"
                      id="oldPassword"
                      value={oldPassword}
                      onChange={(e) => setOldPassword(e.target.value)}
                      required
                      className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderHover} ${themeConfig.borderActive}`}
                    />
                  </div>

                  <div>
                    <label htmlFor="newPassword" className={`block text-sm font-medium ${themeConfig.textSecondary} font-body mb-1`}>
                      {t('settings_new_password')}
                    </label>
                    <input
                      type="password"
                      id="newPassword"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      required
                      className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderHover} ${themeConfig.borderActive}`}
                    />
                  </div>

                  <div>
                    <label htmlFor="confirmPassword" className={`block text-sm font-medium ${themeConfig.textSecondary} font-body mb-1`}>
                      {t('settings_confirm_password')}
                    </label>
                    <input
                      type="password"
                      id="confirmPassword"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderHover} ${themeConfig.borderActive}`}
                    />
                  </div>

                  {error && (
                    <div className={`${themeConfig.errorText} text-sm font-body`}>{error}</div>
                  )}

                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full py-3 px-4 bg-jade-purple-dark hover:bg-jade-purple text-white rounded-lg transition-colors font-body text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? t('settings_updating') : t('settings_update_password')}
                  </button>
                </form>
              </div>
            </div>

            {/* Account Section */}
            <div
              className={`relative ${themeConfig.card} rounded-2xl p-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
              style={theme === 'dark' ? {
                boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              } : {
                boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
              }}
            >
              <div className="relative z-10">
                <h2 className={`text-xl font-bold mb-6 font-title ${themeConfig.text}`}>
                  {t('settings_account_title')}
                </h2>

                <button
                  onClick={confirmSignOut}
                  disabled={isSigningOut}
                  className="w-full py-3 px-4 border border-red-600 bg-transparent text-red-500 hover:bg-red-600 hover:text-white rounded-lg transition-colors font-body text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSigningOut ? t('settings_logging_out') : t('settings_logout')}
                </button>
              </div>
            </div>

            {/* Modals */}
            <LogoutConfirmationModal
              showConfirmation={showLogoutConfirmation}
              isLoading={isSigningOut}
              onCancel={() => setShowLogoutConfirmation(false)}
              onConfirmLogout={handleSignOut}
            />

            <UpdateStatusOverlay
              updateStatus={showSuccessPopup ? 'success' : 'idle'}
              updateProgress={100}
              updateMessage={t('settings_password_success_message')}
              onClose={() => setShowSuccessPopup(false)}
              successText={t('settings_password_updated')}
            />
          </div>
        </div>
      </div>
    </div>
  )
}