'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useThemeConfig } from '@/context/ThemeContext'

export default function DashboardWrapper({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const [isSigningOut, setIsSigningOut] = useState(false)
  const supabase = createClientComponentClient()

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true)

      // Clear server-side cache
      try {
        await fetch('/api/auth/clear-cache', {
          method: 'POST',
          cache: 'no-store'
        })
      } catch (cacheError) {
        console.warn('Failed to clear server cache:', cacheError)
        // Continue with logout even if cache clearing fails
      }

      // Sign out of Supabase (locally)
      const { error } = await supabase.auth.signOut()
      if (error) {
        throw error
      }

      // Clear cookies server-side
      await fetch('/api/auth/signout', {
        method: 'POST',
        cache: 'no-store'
      })

      // Dashboard cache is already cleared above - no separate client info cache needed

      // Redirect to access page using router
      router.push('/access')

      // Refresh the page after navigation for complete state reset
      setTimeout(() => {
        router.refresh()
      }, 100)
    } catch (error) {
      console.error('Error signing out:', error)
      // If we encounter an error, still redirect to access page
      router.push('/access')
    } finally {
      setIsSigningOut(false) // Ensure signing out state is reset
    }
  }


  const themeConfig = useThemeConfig()
  
  return (
    <div className="dashboard-wrapper dashboard-container">
      {children}
    </div>
  )
}
