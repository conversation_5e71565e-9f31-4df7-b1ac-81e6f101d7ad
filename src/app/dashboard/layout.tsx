import { verifyAuth } from '@/utils/auth'
import { redirect } from 'next/navigation'
import DashboardWrapper from './_components/DashboardWrapper'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { authenticated } = await verifyAuth()
  
  if (!authenticated) {
    // Redirect to access page if not authenticated
    redirect('/access')
  }
  
  return (
    <DashboardWrapper>
      {children}
    </DashboardWrapper>
  )
} 