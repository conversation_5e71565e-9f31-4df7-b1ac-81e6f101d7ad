'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { useState, useMemo, useCallback, useRef, useEffect } from 'react'
import { useDashboardProgress } from '@/hooks/useDashboardProgress'
import { usePathname } from 'next/navigation'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import ThemeAwareSkeleton from '@/components/ThemeAwareSkeleton'
import DashboardFooter from '@/components/DashboardFooter'
import { useDashboardData, triggerDashboardRefresh } from '@/hooks/useOptimizedData'
import DashboardTopSection from '@/components/ui/dashboard/DashboardTopSection'
import DashboardTopSectionSkeleton from '@/components/ui/dashboard/DashboardTopSectionSkeleton'
import { FaBrain, FaMicrophone } from 'react-icons/fa'
import { Button, LinkButton } from '@/components/ui'
import AudioPlayer from '@/components/ui/AudioPlayer'
import { 
  EditModal, 
  UpdateConfirmationModal, 
  DeleteConfirmationModal, 
  AudioModeWarningModal, 
  DeleteAudioConfirmationModal
} from '@/components/ui/modals'
import HelpSection from '@/components/ui/knowledge/HelpSection'
import { UpdateStatusOverlay } from '@/components/ui/knowledge'


// Define the type for editing item
interface EditItem {
  id: number;
  field: 'question' | 'answer';
  value: string;
}

export default function Dashboard() {
  const [dismissedWarnings, setDismissedWarnings] = useState({ billing: false, usage: false })
  const pathname = usePathname()
  const { t, language } = useLanguage()
  const { theme, toggleTheme } = useTheme()
  const themeConfig = useThemeConfig()
  
  // Knowledge page state
  const [isUploading, setIsUploading] = useState(false)
  const [question, setQuestion] = useState<string>('')
  const [answer, setAnswer] = useState<string>('')
  const [isAudioAnswer, setIsAudioAnswer] = useState<boolean>(false)
  const [isValidatingAudio, setIsValidatingAudio] = useState<boolean>(false)
  const [audioValidation, setAudioValidation] = useState<{valid: boolean, duration?: number, file_id?: string, previewUrl?: string, error?: string} | null>(null)
  const [recentQA, setRecentQA] = useState<Array<{
    id: number,
    question: string,
    answer: string,
    isAudioAnswer?: boolean,
    audioDuration?: number,
    audioFileId?: string,
    processedAudioBlob?: Blob
  }>>([])
  const [isUpdating, setIsUpdating] = useState(false)
  const [editingItem, setEditingItem] = useState<EditItem | null>(null)
  const [hasFocusedInput, setHasFocusedInput] = useState<boolean>(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<number | null>(null)
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [showAudioModeWarning, setShowAudioModeWarning] = useState(false)
  const [showAudioDeleteConfirm, setShowAudioDeleteConfirm] = useState(false)
  
  // Audio playback state for preview section
  const [selectedPreviewAudioId, setSelectedPreviewAudioId] = useState<string | null>(null)
  const [isPreviewAudioPlaying, setIsPreviewAudioPlaying] = useState(false)
  
  // Audio playback state for Recently Added section
  const [recentQAPreviewUrls, setRecentQAPreviewUrls] = useState<Map<number, string>>(new Map())
  const [selectedRecentAudioId, setSelectedRecentAudioId] = useState<number | null>(null)
  const [isRecentAudioPlaying, setIsRecentAudioPlaying] = useState(false)
  
  const questionInputRef = useRef<HTMLInputElement>(null)
  const answerInputRef = useRef<HTMLInputElement>(null)

  // Use consolidated dashboard data hook - single source of truth
  const { data: dashboardData, loading: isDataLoading, error: dashboardError } = useDashboardData()
  
  // Memoize the handleSaveEdit function
  const handleSaveEdit = useCallback(() => {
    // Use requestAnimationFrame for smoother UI updates
    requestAnimationFrame(() => {
      if (!editingItem) {
        return;
      }

      // Batch state updates
      if (editingItem.id === -1) {
        setQuestion(editingItem.value);
      } else if (editingItem.id === -2) {
        setAnswer(editingItem.value);
        // If this is an answer edit and we're in audio mode, validate the new code
        if (isAudioAnswer && editingItem.value.trim()) {
          validateAudioCode(editingItem.value.trim());
        }
      } else {
        // Use functional update to avoid dependency on recentQA
        setRecentQA(prev =>
          prev.map(qa =>
            qa.id === editingItem.id
              ? { ...qa, [editingItem.field]: editingItem.value }
              : qa
          )
        );
      }

      // Close modal after state updates
      setEditingItem(null);
      setHasFocusedInput(false);
      
    });
  }, [editingItem, isAudioAnswer]);

  // Add effect to focus textarea and set cursor at the end when editing
  // Note: This is now handled by the EditModal component
  useEffect(() => {
    // This effect is kept for compatibility but the actual focus logic 
    // is now handled within the EditModal component
  }, [editingItem, hasFocusedInput])

  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);

  // Function to focus input and set cursor at the end
  const focusInputAtEnd = (ref: React.RefObject<HTMLInputElement>) => {
    if (ref.current) {
      ref.current.focus()
      const length = ref.current.value.length
      ref.current.setSelectionRange(length, length)
    }
  }

  const handleStartEdit = (id: number, field: 'question' | 'answer', value: string) => {
    setEditingItem({ id, field, value })
    setHasFocusedInput(false)
    // Focus and set cursor at end will happen in useEffect
  }

  // Function to validate audio code
  const validateAudioCode = async (audioCode: string) => {
    if (!audioCode.trim()) {
      setAudioValidation(null)
      return
    }

    setIsValidatingAudio(true)
    try {
      const response = await fetch('/api/audio/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ audioCode: audioCode.trim() })
      })

      if (response.ok && response.headers.get('X-Audio-Valid') === 'true') {
        // Response is processed audio blob
        const audioBlob = await response.blob()
        const previewUrl = URL.createObjectURL(audioBlob)
        
        const duration = parseInt(response.headers.get('X-Audio-Duration') || '0')
        const fileId = response.headers.get('X-Audio-FileId') || ''
        
        if (duration > 0) {
          setAudioValidation({
            valid: true,
            duration: duration,
            file_id: fileId,
            previewUrl: previewUrl
          })
        } else {
          // Invalid duration
          URL.revokeObjectURL(previewUrl)
          setAudioValidation(null)
          setAnswer('')
          setUpdateMessage(t('audio_code_incorrect'))
          setUpdateStatus('error')
          setTimeout(() => setUpdateStatus('idle'), 2000)
        }
      } else {
        // Handle JSON error response
        const errorData = await response.json()
        console.error('Audio validation failed:', errorData.error_msg)
        
        // Clear back to normal audio mode
        setAudioValidation(null)
        setAnswer('')
        setUpdateMessage(t('audio_code_incorrect'))
        setUpdateStatus('error')
        setTimeout(() => setUpdateStatus('idle'), 2000)
      }
    } catch (error) {
      console.error('Audio validation error:', error)
      // Clear back to normal audio mode on error
      setAudioValidation(null)
      setAnswer('')
      // Show error message for 2000ms
      setUpdateMessage(t('audio_code_incorrect'))
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 2000)
    } finally {
      setIsValidatingAudio(false)
    }
  }

  // Audio playback handlers for preview section
  const handlePreviewAudioClick = (previewUrl: string) => {
    const audioId = 'preview'
    // Reset Recently Added audio when playing preview
    setSelectedRecentAudioId(null)
    setIsRecentAudioPlaying(false)
    
    if (selectedPreviewAudioId === audioId) {
      // Same audio clicked - toggle play/pause
      setIsPreviewAudioPlaying(!isPreviewAudioPlaying)
    } else {
      // Different audio clicked - select new one and start playing
      setSelectedPreviewAudioId(audioId)
      setIsPreviewAudioPlaying(true)
    }
  }

  // Audio playback handlers for Recently Added section
  const handleRecentAudioClick = (id: number) => {
    // Reset preview audio when playing recent audio
    setSelectedPreviewAudioId(null)
    setIsPreviewAudioPlaying(false)
    
    if (selectedRecentAudioId === id) {
      // Same audio clicked - toggle play/pause
      setIsRecentAudioPlaying(!isRecentAudioPlaying)
    } else {
      // Different audio clicked - select new one and start playing
      setSelectedRecentAudioId(id)
      setIsRecentAudioPlaying(true)
    }
  }

  // Create preview URLs for Recently Added audio items
  useEffect(() => {
    const newUrls = new Map<number, string>()
    
    recentQA.forEach(qa => {
      if (qa.processedAudioBlob) {
        const previewUrl = URL.createObjectURL(qa.processedAudioBlob)
        newUrls.set(qa.id, previewUrl)
      }
    })
    
    // Clean up old URLs
    recentQAPreviewUrls.forEach((url, id) => {
      if (!newUrls.has(id)) {
        URL.revokeObjectURL(url)
      }
    })
    
    setRecentQAPreviewUrls(newUrls)
    
    // Cleanup on unmount
    return () => {
      newUrls.forEach(url => URL.revokeObjectURL(url))
    }
  }, [recentQA])

  // Handle audio delete confirmation
  const handleConfirmAudioDelete = () => {
    if (audioValidation?.previewUrl) {
      URL.revokeObjectURL(audioValidation.previewUrl)
    }
    setAudioValidation(null)
    setAnswer('')
    setShowAudioDeleteConfirm(false)
  }

  const handleAddQA = async () => {
    if (question.trim() && answer.trim()) {
      // For audio answers, check if validation passed
      if (isAudioAnswer && (!audioValidation?.valid)) {
        setUpdateMessage('Please provide a valid audio code')
        setUpdateStatus('error')
        setTimeout(() => setUpdateStatus('idle'), 3000)
        return
      }

      // Get processed audio blob for storage
      let processedAudioBlob: Blob | undefined
      if (isAudioAnswer && audioValidation?.valid && audioValidation.previewUrl) {
        try {
          // Convert preview URL back to blob for storage
          const response = await fetch(audioValidation.previewUrl)
          processedAudioBlob = await response.blob()
        } catch (error) {
          console.error('Error converting preview URL to blob:', error)
        }
      }

      setRecentQA(prev => [...prev, {
        id: Date.now(),
        question: question.trim(),
        answer: answer.trim(),
        isAudioAnswer: isAudioAnswer,
        audioDuration: isAudioAnswer && audioValidation?.valid ? audioValidation.duration : undefined,
        audioFileId: isAudioAnswer && audioValidation?.valid ? audioValidation.file_id : undefined,
        processedAudioBlob: processedAudioBlob
      }]);

      // Reset fields after adding
      setQuestion('');
      setAnswer('');
      // Reset audio validation but keep audio mode
      if (audioValidation?.previewUrl) {
        URL.revokeObjectURL(audioValidation.previewUrl);
      }
      setAudioValidation(null);
      
    }
  }

  // Prompt for confirmation before update
  const handleUpdate = () => {
    if (recentQA.length === 0) {
      setUpdateMessage(t('no_questions_update'))
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }
    if (recentQA.length > 10) {
      setUpdateMessage('Maximum 10 FAQs per update. Please remove some items.')
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }
    setShowConfirmation(true)
  }

  // Helper function to convert blob to base64
  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onerror = reject;
      reader.onload = () => {
        const base64String = reader.result as string;
        resolve(base64String.split(',')[1]); // Remove the data URL prefix
      };
      reader.readAsDataURL(blob);
    });
  }

  // Save Q&A items using API route (which uses PostgreSQL webhook)
  const saveQAToSupabase = async () => {
    setShowConfirmation(false);
    setIsUpdating(true);
    setUpdateStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage(t('processing_additions'));

    const totalItems = recentQA.length;
    if (totalItems === 0) {
      setUpdateMessage(t('no_questions_update'));
      setUpdateStatus('error');
      setTimeout(() => setUpdateStatus('idle'), 3000);
      setIsUpdating(false);
      return;
    }

    // Start realistic progress animation (5 seconds to 90%)
    let progressStep = 0;
    const progressInterval = setInterval(() => {
      setUpdateProgress(prev => {
        if (prev >= 90) return prev; // Stop at 90% until real response
        
        progressStep++;
        // Realistic progress curve: slow start, fast middle, slow end
        if (progressStep <= 4) return prev + 3; // 0-12%: slow start (0.8s)
        if (progressStep <= 12) return prev + 6; // 12-60%: fast processing (1.6s)  
        if (progressStep <= 20) return prev + 3; // 60-84%: slow end (1.6s)
        return Math.min(90, prev + 1); // 84-90%: final stretch (1s)
      });
    }, 200); // 200ms intervals = smoother animation

    try {
      // Prepare knowledge items for API
      const knowledgeItems = []
      const audioCount = recentQA.filter(qa => qa.isAudioAnswer).length
      
      if (audioCount > 0) {
        setUpdateMessage(`Uploading ${audioCount} audio file${audioCount > 1 ? 's' : ''}...`);
      } else {
        setUpdateMessage('Preparing FAQs for upload...');
      }

      // Use FormData to send blobs
      const formData = new FormData()
      
      for (let i = 0; i < recentQA.length; i++) {
        const qa = recentQA[i]
        
        // Add FAQ data
        knowledgeItems.push({
          question: qa.question,
          answer: qa.answer,
          isAudioAnswer: qa.isAudioAnswer || false,
          audioFileId: qa.audioFileId,
          audioDuration: qa.audioDuration,
          // Add index reference for blob mapping
          audioIndex: qa.isAudioAnswer && qa.processedAudioBlob ? i : null
        });
        
        // Add audio blob if it exists
        if (qa.isAudioAnswer && qa.processedAudioBlob) {
          formData.append(`audioBlob_${i}`, qa.processedAudioBlob, `audio_${i}.m4a`)
        }
      }

      // Add FAQ batch as JSON string
      formData.append('faqBatch', JSON.stringify(knowledgeItems))
      
      // Add sector and lang from dashboard cache to eliminate API cache lookup
      if (dashboardData?.clientInfo?.sector) {
        formData.append('sector', dashboardData.clientInfo.sector)
      }
      if (dashboardData?.clientInfo?.lang) {
        formData.append('lang', dashboardData.clientInfo.lang)
      }

      // Show processing message
      if (audioCount > 0) {
        setUpdateMessage('Uploading audio files and saving to database...');
      } else {
        setUpdateMessage('Saving FAQs to database...');
      }

      // Call new batch API route
      const response = await fetch('/api/knowledge/add-batch', {
        method: 'POST',
        body: formData,
      });

      let responseData = await response.json();
      
      // Handle array response format from N8N webhook
      if (Array.isArray(responseData) && responseData.length > 0) {
        responseData = responseData[0]
      }

      if (!response.ok || (!responseData.success && !responseData.partial_success)) {
        throw new Error(responseData.error_msg || 'Failed to save knowledge items');
      }

      // Clear progress interval and smoothly fill to 100%
      clearInterval(progressInterval);
      
      // Function to handle success/error after animation completes
      const handleSuccessMessage = (responseData: any) => {
        // Handle partial success (some FAQs failed)
        if (responseData.partial_success) {
          const successCount = responseData.body?.items_processed || 0;
          const failedCount = responseData.body?.items_failed || 0;
          
          // Remove only the successful FAQs from the UI list (first N items)
          if (successCount > 0) {
            setRecentQA(prev => prev.slice(successCount));
          }

          // Trigger dashboard refresh to update FAQ count
          triggerDashboardRefresh();

          setUpdateStatus('error');
          setUpdateMessage(responseData.error_msg || `${successCount} FAQs saved successfully, ${failedCount} failed. Please retry the remaining items.`);

          setTimeout(() => {
            setUpdateStatus('idle');
            setIsUpdating(false);
          }, 3000); // Error timeout
        } else {
          // Full success - all FAQs processed successfully
          const count = recentQA.length;
          const plural = count !== 1 ? 's' : '';
          setUpdateMessage(t('update_success').replace('{count}', count.toString()).replace('{plural}', plural));
          setUpdateStatus('success');

          // Trigger dashboard refresh to update FAQ count
          triggerDashboardRefresh();

          // Clear the recent QA list
          setRecentQA([]);

          // Show success message and reset
          setTimeout(() => {
            setUpdateStatus('idle');
            setIsUpdating(false);
          }, 1500);
        }
      };
      
      // Smooth fill to 100% directly
      setTimeout(() => {
        setUpdateProgress(100);
        // Wait a bit at 100%, then show success
        setTimeout(() => {
          handleSuccessMessage(responseData);
        }, 300);
      }, 50);

    } catch (error: any) {
      console.error('Error updating knowledge base:', error);

      // Clear progress interval on error
      clearInterval(progressInterval);

      // Check for browser blocking errors
      if (error.name === 'AbortError' || 
          error.message?.includes('blocked') || 
          error.message?.includes('ERR_BLOCKED_BY_CLIENT') ||
          error.message?.includes('Failed to fetch')) {
        
        setUpdateStatus('error');
        setUpdateMessage('Request blocked by browser security. Please try: 1) Disable ad-blocker/shields for this site, 2) Use Chrome browser, or 3) Disable VPN temporarily.');
        
        setTimeout(() => {
          setUpdateStatus('idle');
          setIsUpdating(false);
        }, 5000); // Longer timeout for browser-specific message
        return;
      }

      // Check if this is a partial success (some FAQs succeeded, some failed)
      if (error.response && error.response.data?.partial_success) {
        const responseData = error.response.data;
        const successfulFaqIds = responseData.body?.successful_faq_ids || [];
        
        // Remove successful FAQs from the recent list
        if (successfulFaqIds.length > 0) {
          setRecentQA(prev => prev.filter(qa => {
            // Map the recent QA to find matching FAQ IDs (this is tricky since we don't store faq_id in recent)
            // For now, remove the first N successful items
            const successfulCount = successfulFaqIds.length;
            const index = prev.indexOf(qa);
            return index >= successfulCount; // Keep only the failed ones (last items)
          }));
        }

        // Trigger dashboard refresh to update FAQ count
        if (responseData.body?.items_processed > 0) {
          triggerDashboardRefresh();
        }

        setUpdateStatus('error');
        const errorSuccessCount = responseData.body?.items_processed || 0;
        setUpdateMessage(responseData.error_msg || `${errorSuccessCount} FAQs saved successfully, ${responseData.body?.items_failed || 0} failed. Please retry the remaining items.`);
      } else {
        setUpdateStatus('error');
        setUpdateMessage(error.message || 'Failed to save knowledge items');
      }

      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUpdating(false);
      }, 3000); // Error timeout
    }
  };

  const handleDelete = (id: number) => {
    // Show confirmation modal instead of deleting immediately
    setItemToDelete(id);
    setShowDeleteConfirmation(true);
  }

  // Function to handle actual deletion after confirmation
  const confirmDelete = () => {
    if (itemToDelete === null) return;

    const qaToDelete = recentQA.find(qa => qa.id === itemToDelete);

    // Clean up audio blob URL if this is an audio item
    if (qaToDelete?.isAudioAnswer && recentQAPreviewUrls.has(itemToDelete)) {
      const audioUrl = recentQAPreviewUrls.get(itemToDelete);
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    }

    // Remove item after confirmation
    setRecentQA(prev => prev.filter(qa => qa.id !== itemToDelete));

    // Close the confirmation modal
    setShowDeleteConfirmation(false);
    setItemToDelete(null);
  }

  // Function to confirm switching to audio mode
  const confirmAudioMode = () => {
    setAnswer('');
    setIsAudioAnswer(true);
    setShowAudioModeWarning(false);
  }

  // Simple scroll disable when any popup is open
  useEffect(() => {
    const hasOpenModal = showConfirmation || editingItem || showDeleteConfirmation || showAudioModeWarning || showAudioDeleteConfirm || (updateStatus !== 'idle')

    if (hasOpenModal) {
      document.body.style.overflow = 'hidden'
      // Pause any playing audio when modal opens
      setSelectedPreviewAudioId(null)
      setIsPreviewAudioPlaying(false)
      setSelectedRecentAudioId(null)
      setIsRecentAudioPlaying(false)
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [showConfirmation, editingItem, showDeleteConfirmation, showAudioModeWarning, showAudioDeleteConfirm, updateStatus])

  // Extract client info from dashboard data
  const clientInfo = dashboardData?.clientInfo || null

  // Extract data from consolidated hook - memoized
  const subscriptionData = useMemo(() => {
    return dashboardData?.clientInfo ? {
      plan_type: dashboardData.clientInfo.plan_type,
      next_billing_date: dashboardData.clientInfo.next_billing_date
    } : { plan_type: null, next_billing_date: null }
  }, [dashboardData?.clientInfo, language])

  const usageData = dashboardData?.usageData || { usage_used: 0, usage_limit: 100, cmt_used: 0, cmt_limit: 2000 }

  // Extract knowledge stats from consolidated data
  const knowledgeStats = dashboardData?.knowledgeStats
  const totalFaqs = knowledgeStats?.faqCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0

  // Comment stats will be provided by useDashboardProgress hook


  // Removed automatic sync between UI language and bot language
  // UI language (browser) and bot language (database) are completely separate systems

  // Authentication is handled by server-side layout protection

  // Format the date to a more readable format - memoized
  const formattedBillingDate = useMemo(() => {
    const formatDate = (dateString: string | null) => {
      if (!dateString) return 'Not available';
      try {
        // Convert UTC date to local timezone for proper comparison
        const utcDate = new Date(dateString);
        // Convert to local date string (YYYY-MM-DD format)
        const localBillingDate = utcDate.toLocaleDateString('en-CA', { timeZone: 'Asia/Phnom_Penh' });

        // Format for display
        const displayDate = new Date(localBillingDate);
        return displayDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      } catch (error) {
        console.error('Error formatting date:', error);
        return 'Invalid date';
      }
    };
    return formatDate(subscriptionData.next_billing_date);
  }, [subscriptionData.next_billing_date]);

  // Check if billing date is overdue
  const isBillingOverdue = () => {
    // Check user status for expired plan
    return dashboardData?.clientInfo?.status === 'expired';
    
    // OLD DATE-BASED LOGIC (commented out)
    // if (!subscriptionData.next_billing_date) return false;
    // 
    // // OLD LOGIC (commented out)
    // // const billingDate = new Date(subscriptionData.next_billing_date);
    // // // Get current date in Asia/Phnom_Penh timezone
    // // const today = new Date();
    // // const todayInPhnomPenh = new Date(today.toLocaleString('en-US', { timeZone: 'Asia/Phnom_Penh' }));
    // // todayInPhnomPenh.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison
    // // return billingDate < todayInPhnomPenh;
    // 
    // // NEW LOGIC - simplified ISO date string comparison
    // const today = new Date().toISOString().split('T')[0]; 
    // // today = "2025-07-12"
    // 
    // const billingDate = subscriptionData.next_billing_date.split('T')[0]; 
    // // billingDate = "2025-08-12" 
    // 
    // return today > billingDate;
    // // "2025-07-12" > "2025-08-12" = false ✅
  };

  // Unified progress calculations
  const {
    messageUsagePercentage,
    commentUsagePercentage,
    isUsageLimitReached,
    messageUsed,
    messageLimit,
    commentUsed,
    commentLimit
  } = useDashboardProgress({
    usageData,
    isDashboardLoading: isDataLoading
  });

  // Optimized warning dismiss handler
  const handleDismissWarning = useCallback((type: 'billing' | 'usage') => {
    setDismissedWarnings(prev => ({ ...prev, [type]: true }));
  }, []);

  // Simplified loading state management - reduced delays for faster response
  const [showHeaderSkeleton, setShowHeaderSkeleton] = useState(false)
  useEffect(() => {
    let timer: ReturnType<typeof setTimeout> | null = null
    if (isDataLoading) {
      timer = setTimeout(() => setShowHeaderSkeleton(true), 80) // reduced from 120ms to 80ms
    } else {
      setShowHeaderSkeleton(false) // immediately switch to content, AnimatePresence will crossfade
    }
    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [isDataLoading])

  // Simplified body reveal - show immediately when data is ready (no additional delay)
  const [showBody, setShowBody] = useState(false)
  useEffect(() => {
    if (!isDataLoading) {
      // Show body immediately when data is ready - no artificial delay
      setShowBody(true)
    } else {
      setShowBody(false)
    }
  }, [isDataLoading])

  return (
    <div className={themeConfig.pageBackground}>
      {/* Reserve header space to minimize layout shift */}
      <div className="min-h-[168px]">
        <AnimatePresence mode="wait">
          {showHeaderSkeleton ? (
            <motion.div
              key="header-skeleton"
              initial={{ opacity: 0, y: 4 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -4 }}
              transition={{ duration: 0.2, ease: 'easeOut' }}
            >
              <DashboardTopSectionSkeleton />
            </motion.div>
          ) : (
            <motion.div
              key="header-content"
              initial={{ opacity: 0, y: 4 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -4 }}
              transition={{ duration: 0.2, ease: 'easeOut' }}
            >
              <DashboardTopSection
        clientInfo={clientInfo}
        subscriptionData={subscriptionData}
        formattedBillingDate={formattedBillingDate}
        currentPath={pathname}
        totalFaqs={totalFaqs}
        totalFaqsLimit={totalFaqsLimit}
        photoCount={0}
        photoLimit={0}
        faqUsagePercentage={faqUsagePercentage}
        photoUsagePercentage={0}
        isLoadingCount={isDataLoading}
        messageUsed={messageUsed}
        messageLimit={messageLimit}
        messageUsagePercentage={messageUsagePercentage}
        commentUsed={commentUsed}
        commentLimit={commentLimit}
        commentUsagePercentage={commentUsagePercentage}
        isDashboardView={true}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Dashboard Content */}
      <AnimatePresence>
        {showBody && (
          <motion.div
            key="dashboard-body"
            className="flex-grow container mx-auto px-4 py-3"
            initial={{ opacity: 0, y: 4 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -4 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
            layout
          >
          {/* Error handling for dashboard data */}
          {dashboardError && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className={`${themeConfig.errorBackground} backdrop-blur-md border ${themeConfig.errorBorder} rounded-xl p-4 mb-8 ${themeConfig.errorText} ${themeConfig.errorShadow}`}
            >
              <h2 className="text-xl font-semibold mb-2">Error Loading Dashboard</h2>
              <p className="mb-4">Unable to load dashboard data. Please try refreshing the page.</p>
              <p className="text-sm">{dashboardError}</p>
            </motion.div>
          )}

          {!clientInfo && !isDataLoading && !dashboardError && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className={`${themeConfig.warningBackground} backdrop-blur-md border ${themeConfig.warningBorder} rounded-xl p-4 mb-8 ${themeConfig.warningText} ${themeConfig.warningShadow}`}
            >
              <h2 className="text-xl font-semibold mb-2">{t('account_setup_incomplete')}</h2>
              <p className="mb-4">{t('account_setup_message')}</p>
              <p>{t('email')} {dashboardData?.clientInfo?.username}</p>
              <p className="text-sm mt-2">{t('contact_support')}</p>
            </motion.div>
          )}

          {/* Warning Messages */}
          {(isBillingOverdue() || isUsageLimitReached) && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="mb-6"
            >
              {isBillingOverdue() && !dismissedWarnings.billing && (
                <div className={`${themeConfig.errorBackground} backdrop-blur-md border ${themeConfig.errorBorder} rounded-xl p-4 mb-4 ${themeConfig.errorText} ${themeConfig.errorShadow} relative`}>
                  <button
                    onClick={() => handleDismissWarning('billing')}
                    className="absolute top-1/2 -translate-y-1/2 right-3 w-6 h-6 bg-red-500/20 hover:bg-red-500/30 rounded-full flex items-center justify-center text-red-400 hover:text-red-300 transition-all duration-200"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                  <div className="flex items-center space-x-3 pr-8">
                    <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-4 h-4 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <p className="text-sm font-body">{t('plan_expired_warning')}</p>
                  </div>
                </div>
              )}

              {isUsageLimitReached && !dismissedWarnings.usage && (
                <div className={`${themeConfig.usageWarningBackground} backdrop-blur-md border ${themeConfig.usageWarningBorder} rounded-xl p-4 mb-4 ${themeConfig.usageWarningText} ${themeConfig.usageWarningShadow} relative`}>
                  <button
                    onClick={() => handleDismissWarning('usage')}
                    className="absolute top-1/2 -translate-y-1/2 right-3 w-6 h-6 bg-orange-500/20 hover:bg-orange-500/30 rounded-full flex items-center justify-center text-orange-400 hover:text-orange-300 transition-all duration-200"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                  <div className="flex items-center space-x-3 pr-8">
                    <div className="w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-4 h-4 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <p className="text-sm font-body">{t('usage_limit_warning')}</p>
                  </div>
                </div>
              )}
            </motion.div>
          )}


          {/* Main Dashboard Content */}
          {isDataLoading ? (
            <ThemeAwareSkeleton />
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 8 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2, ease: 'easeOut' }}
            >
              {/* Business Insights Section */}
              <div
                className={`relative ${themeConfig.card} rounded-2xl p-6 mb-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
                style={theme === 'dark' ? {
                  boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                } : {
                  boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
                }}
              >
                <div className="relative z-10">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className={`text-xl font-bold font-title ${themeConfig.text}`}>{t('business_insights')}</h2>
                    <LinkButton
                      href="/dashboard/knowledge/knowledgeBase"
                      variant="secondary"
                      size="sm"
                      className="text-xs sm:text-base p-2 px-3 border-2"
                    >
                      {t('manage')}
                    </LinkButton>
                  </div>

                  {/* Help Section */}
                  <HelpSection 
                    youtubeUrl="#" // TODO: Replace with actual YouTube URL
                    message={t('add_business_info')}
                    clientLang={clientInfo?.lang || 'English'}
                  />

                  <div className="grid grid-cols-1 sm:grid-cols-12 gap-3 mb-6">
                    {/* Question Trigger */}
                    <div className="sm:col-span-5">
                      <div
                        className={`px-2 md:px-4 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderActive} cursor-pointer ${themeConfig.borderHover} flex items-center min-h-[42px]`}
                        onClick={() => {
                          setEditingItem({
                            id: -1,
                            field: 'question',
                            value: question
                          });
                        }}
                      >
                        {question ?
                          <span className="truncate font-body">{question}</span> :
                          <span className={`${themeConfig.textMuted} truncate font-body`}>{t('enter_question')}</span>
                        }
                      </div>
                    </div>
                    {/* Answer Trigger */}
                    <div className="sm:col-span-5 relative">
                      <div
                        className={`px-2 pr-10 md:px-4 md:pr-10 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} focus:outline-none ${themeConfig.borderActive} ${audioValidation?.valid ? 'cursor-default' : `cursor-pointer ${themeConfig.borderHover}`} flex items-center min-h-[42px] overflow-hidden min-w-0`}
                        onClick={audioValidation?.valid ? undefined : () => {
                          setEditingItem({
                            id: -2,
                            field: 'answer',
                            value: answer
                          });
                        }}
                      >
                        {/* Regular input display */}
                        {answer ? (
                          <div className="flex items-center w-full">
                            {isAudioAnswer && audioValidation?.valid && audioValidation.previewUrl ? (
                              <AudioPlayer
                                audioUrl={audioValidation.previewUrl}
                                compact={true}
                                className="mr-2"
                                isSelected={selectedPreviewAudioId === 'preview'}
                                isPlaying={selectedPreviewAudioId === 'preview' && isPreviewAudioPlaying}
                                onClick={() => handlePreviewAudioClick(audioValidation.previewUrl!)}
                              />
                            ) : isAudioAnswer && audioValidation?.valid ? (
                              <div className="flex items-center mr-2 flex-shrink-0">
                                <svg 
                                  className={`w-4 h-4 mr-1 ${themeConfig.text}`}
                                  fill="currentColor" 
                                  viewBox="0 0 24 24"
                                >
                                  <rect x="2" y="8" width="2" height="8" rx="1"/>
                                  <rect x="6" y="5" width="2" height="14" rx="1"/>
                                  <rect x="10" y="10" width="2" height="4" rx="1"/>
                                  <rect x="14" y="3" width="2" height="18" rx="1"/>
                                  <rect x="18" y="7" width="2" height="10" rx="1"/>
                                  <rect x="22" y="12" width="2" height="2" rx="1"/>
                                </svg>
                                <span className={`text-sm ${themeConfig.textMuted}`}>
                                  {audioValidation.duration}s
                                </span>
                              </div>
                            ) : isValidatingAudio ? (
                              <div className="mr-2 w-4 h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin flex-shrink-0"></div>
                            ) : null}
                            <span className="truncate flex-1 min-w-0 font-body">{isAudioAnswer && (audioValidation?.valid || isValidatingAudio) ? '' : answer}</span>
                          </div>
                        ) : (
                          <span className={`${themeConfig.textMuted} truncate pr-12 min-w-0 font-body`}>
                            {isAudioAnswer ? t('paste_audio_code') : t('enter_reply')}
                          </span>
                        )}
                        
                        {/* Microphone/Trash Icon */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent triggering the edit modal
                            
                            // Prevent clicks during audio validation
                            if (isValidatingAudio) {
                              return;
                            }
                            
                            if (isAudioAnswer && audioValidation?.valid) {
                              // Show confirmation before clearing audio
                              setShowAudioDeleteConfirm(true);
                            } else if (!isAudioAnswer) {
                              // Check if there's existing text
                              if (answer.trim()) {
                                setShowAudioModeWarning(true);
                              } else {
                                setIsAudioAnswer(true);
                              }
                            } else {
                              setIsAudioAnswer(false);
                            }
                          }}
                          disabled={isValidatingAudio}
                          className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full transition-all duration-200 ${
                            isValidatingAudio
                              ? 'opacity-50 cursor-not-allowed'
                              : isAudioAnswer && audioValidation?.valid
                                ? 'bg-red-500/20 text-red-500 hover:bg-red-500/30'
                                : isAudioAnswer 
                                  ? 'bg-jade-purple-dark text-white hover:bg-jade-purple-dark/80' 
                                  : `${themeConfig.textMuted} hover:text-jade-purple hover:bg-jade-purple/10`
                          }`}
                          title={
                            isValidatingAudio
                              ? 'Processing audio...'
                              : isAudioAnswer && audioValidation?.valid 
                                ? 'Clear audio code'
                                : isAudioAnswer 
                                  ? 'Audio mode active - click to switch to text' 
                                  : 'Switch to audio answer'
                          }
                        >
                          {isAudioAnswer && audioValidation?.valid ? (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          ) : (
                            <FaMicrophone className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </div>
                    {/* Add Button */}
                    <div className="sm:col-span-2 flex items-center justify-center">
                      <Button
                        onClick={handleAddQA}
                        variant="primary"
                        size="md"
                        className="text-xs sm:text-base w-full"
                        disabled={!question.trim() || !answer.trim() || (isAudioAnswer && (!audioValidation?.valid || isValidatingAudio))}
                      >
                        {t('add')}
                      </Button>
                    </div>
                  </div>

                  {/* Recently Added Section - now part of the same container */}
                  <div className={`flex justify-between items-center mb-3 border-t ${themeConfig.divider} pt-6`}>
                    <h3 className={`text-lg font-semibold font-title ${themeConfig.text}`}>{t('recently_added')}</h3>
                    <Button
                      onClick={handleUpdate}
                      variant="primary"
                      size="md"
                      className="text-xs sm:text-base"
                      disabled={isUpdating || recentQA.length === 0}
                    >
                      {isUpdating ? t('updating') : t('update')}
                    </Button>
                  </div>

                  {/* Column Headers */}
                  <div className={`flex border-b ${themeConfig.divider} py-3 font-semibold ${themeConfig.textSecondary} font-body`}>
                    <div className="w-[5%] px-2 text-left"></div>
                    <div className="w-[35%] px-2 text-left">{t('question')}</div>
                    <div className="w-[35%] px-2 text-left">{t('reply')}</div>
                    <div className="w-[15%] px-2 text-left"></div>
                    <div className="w-[10%] px-2 text-center"></div>
                  </div>

                  {/* List Items */}
                  <div className="w-full">
                    {recentQA.length > 0 ? (
                      recentQA.map((qa, index) => (
                        <div key={qa.id} className={`flex border-b ${themeConfig.divider} py-3 items-center`}>
                          <div className={`w-[5%] px-2 text-left ${themeConfig.textMuted} font-body`}>
                            {index + 1}
                          </div>
                          <div
                            className="w-[35%] px-2 py-1"
                          >
                            <div
                              className={`h-full w-full truncate overflow-hidden min-w-0 px-2 py-1.5 ${themeConfig.secondCard} border-2 ${themeConfig.border} rounded-lg ${themeConfig.secondCardHover} flex items-center group cursor-pointer transition-all`}
                              title={qa.question}
                              onClick={() => handleStartEdit(qa.id, 'question', qa.question)}
                            >
                              <span className={`flex-1 truncate min-w-0 ${themeConfig.textSecondary} font-body`}>{qa.question}</span>
                              <svg className={`w-4 h-4 ml-2 ${themeConfig.textMuted} group-hover:text-jade-purple invisible group-hover:visible`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </div>
                          </div>
                          <div
                            className="w-[42.5%] px-2 py-1"
                          >
                            <div
                              className={`h-full w-full truncate overflow-hidden min-w-0 px-2 py-1.5 ${themeConfig.secondCard} border-2 ${themeConfig.border} rounded-lg ${themeConfig.secondCardHover} flex items-center group transition-all`}
                              title={qa.answer}
                              onClick={qa.isAudioAnswer ? undefined : () => handleStartEdit(qa.id, 'answer', qa.answer)}
                            >
                              {qa.isAudioAnswer ? (
                                recentQAPreviewUrls.has(qa.id) ? (
                                  <AudioPlayer
                                    audioUrl={recentQAPreviewUrls.get(qa.id)!}
                                    compact={true}
                                    isSelected={selectedRecentAudioId === qa.id}
                                    isPlaying={selectedRecentAudioId === qa.id && isRecentAudioPlaying}
                                    onClick={() => handleRecentAudioClick(qa.id)}
                                  />
                                ) : (
                                  <div className="flex items-center">
                                    <svg 
                                      className={`w-4 h-4 mr-1 ${themeConfig.text}`}
                                      fill="currentColor" 
                                      viewBox="0 0 24 24"
                                    >
                                      <rect x="2" y="8" width="2" height="8" rx="1"/>
                                      <rect x="6" y="5" width="2" height="14" rx="1"/>
                                      <rect x="10" y="10" width="2" height="4" rx="1"/>
                                      <rect x="14" y="3" width="2" height="18" rx="1"/>
                                      <rect x="18" y="7" width="2" height="10" rx="1"/>
                                      <rect x="22" y="12" width="2" height="2" rx="1"/>
                                    </svg>
                                    <span className={`text-sm ${themeConfig.textMuted} flex-shrink-0`}>
                                      {qa.audioDuration ? `${t('voice')} ${qa.audioDuration}s` : t('voice')}
                                    </span>
                                  </div>
                                )
                              ) : (
                                <>
                                  <span className={`flex-1 truncate min-w-0 ${themeConfig.textSecondary} font-body`}>{qa.answer}</span>
                                  <svg className={`w-4 h-4 ml-2 ${themeConfig.textMuted} group-hover:text-jade-purple invisible group-hover:visible`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                  </svg>
                                </>
                              )}
                            </div>
                          </div>
                          <div className="w-[10%] px-2 flex justify-center">
                            <button
                              className="p-2 rounded-full bg-red-500/20 hover:bg-red-500/30 text-red-500 transition-colors"
                              onClick={() => handleDelete(qa.id)}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className={`py-8 text-center ${themeConfig.textMuted}`}>
                        {t('no_questions_update')}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          )}
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* FAQ Update Status Overlay */}
      <UpdateStatusOverlay
        updateStatus={updateStatus}
        updateProgress={updateProgress}
        updateMessage={updateMessage}
        onClose={() => setUpdateStatus('idle')}
        loadingText={`${t('updating')}...`}
        completeText={t('complete')}
        successText={t('success')}
        errorText={t('error')}
      />

      {/* Confirmation Modal */}
      <UpdateConfirmationModal
        showConfirmation={showConfirmation}
        onCancel={() => setShowConfirmation(false)}
        onConfirm={saveQAToSupabase}
        count={recentQA.length}
      />

      {/* Edit Modal */}
      <EditModal
        editingItem={editingItem}
        hasFocusedInput={hasFocusedInput}
        onValueChange={(value) => {
          setEditingItem(prev => prev ? {...prev, value} : null);
        }}
        onInputFocus={() => setHasFocusedInput(true)}
        onSave={handleSaveEdit}
        onClose={() => setEditingItem(null)}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        deleteConfirm={itemToDelete ? { id: itemToDelete } : null}
        isDeleting={false}
        onCancel={() => {
          setShowDeleteConfirmation(false);
          setItemToDelete(null);
        }}
        onConfirmDelete={confirmDelete}
      />

      {/* Audio Mode Warning Modal */}
      <AudioModeWarningModal
        showAudioModeWarning={showAudioModeWarning}
        onCancel={() => setShowAudioModeWarning(false)}
        onConfirm={confirmAudioMode}
      />

      {/* Audio Delete Confirmation Modal */}
      <DeleteAudioConfirmationModal
        showDeleteAudioConfirm={showAudioDeleteConfirm}
        onCancel={() => setShowAudioDeleteConfirm(false)}
        onConfirmDelete={handleConfirmAudioDelete}
      />
      
      <AnimatePresence>
        {showBody && (
          <motion.div
            key="dashboard-footer"
            initial={{ opacity: 0, y: 4 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -4 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
          >
            <DashboardFooter />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
