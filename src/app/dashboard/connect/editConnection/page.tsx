'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/context/AuthContext'
import { useDashboardData } from '@/hooks/useOptimizedData'
import { FaFacebookMessenger, FaInstagram, FaPaperPlane, FaGlobe } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { DashboardHeader, PlatformCard, LinkButton } from '@/components/ui'
import { UpdateStatusOverlay } from '@/components/ui/knowledge'
import { DeleteConfirmationModal } from '@/components/ui/modals'

// Define platform type for documentation purposes
// 'facebook' | 'instagram' | 'whatsapp' | 'telegram' | 'web' | 'tiktok';

// Define sanitized client credentials type (from API)
type ClientCredentials = {
  id: number;
  client_id: string;
  fb_name?: string;
  ig_name?: string;
  tg_name?: string;
  wa_name?: string;
  web_name?: string;
  web_domain?: string;
  fb_status?: number;
  ig_status?: number;
  wa_status?: number;
  tg_status?: number;
  web_status?: number;
  tg_connection_type?: 'Bot' | 'Business';
  // Security: URLs, tokens, and sensitive identifiers are not exposed
};

export default function EditConnectionPage() {
  // const { user: _ } = useAuth() // Unused but kept for context
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Use dashboard cache for client info
  const { data: dashboardData } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo

  // State variables
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  // Overlay state for UpdateStatusOverlay
  const [overlayStatus, setOverlayStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [overlayMessage, setOverlayMessage] = useState<string>('')
  const [isClient, setIsClient] = useState(false)
  const [credentials, setCredentials] = useState<ClientCredentials | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Note: Connection limits not needed for edit page (only shows connected platforms)

  // State for delete confirmation - adapted for DeleteConfirmationModal
  const [deleteConfirm, setDeleteConfirm] = useState<{ id: number; platform?: string } | null>(null)

  // State for tracking if deletion is in progress
  const [isDeleting, setIsDeleting] = useState(false)

  // Track connected platforms
  const [connectedPlatforms, setConnectedPlatforms] = useState<{[key: string]: boolean}>({
    facebook: false,
    instagram: false,
    /* whatsapp: false, */
    telegram: false,
    web: false
  })

  // Note: Removed token values and input refs since this page only shows connected platforms for disconnection

  // Removed deleteConfirmModalRef - now using onClick on backdrop for better performance

  // Set isClient to true when component mounts
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Disable page scroll when any popup is open
  useEffect(() => {
    if (errorMessage || successMessage || overlayStatus !== 'idle' || deleteConfirm) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [errorMessage, successMessage, overlayStatus, deleteConfirm])

  // Guard to avoid double-fetch in React 18 Strict Mode (dev)
  const hasFetchedCredentials = useRef(false)

  // Fetch connection data when component mounts (guarded)
  useEffect(() => {
    if (hasFetchedCredentials.current) return
    hasFetchedCredentials.current = true
    fetchClientCredentials();
  }, [])

  // Removed useEffect for click outside - now using onClick on backdrop for better performance

  // Helper functions for overlay management
  const showError = (message: string) => {
    setOverlayStatus('error')
    setOverlayMessage(message)
  }

  const showSuccess = (message: string) => {
    setOverlayStatus('success')
    setOverlayMessage(message)
    
    // Auto-hide success overlay after 1.5 seconds
    setTimeout(() => {
      hideOverlay()
    }, 1500)
  }

  const hideOverlay = () => {
    setOverlayStatus('idle')
    setOverlayMessage('')
  }

  // Note: Removed fetchSubscriptionLimits since connection limits are not needed for disconnection

  // Fetch connected platforms via specialized API
  const fetchClientCredentials = async () => {
    try {
      setIsLoading(true)

      // Call specialized connected platforms API
      const response = await fetch('/api/connection-data/connected')
      const responseData = await response.json()

      if (!response.ok) {
        console.error('Error fetching connected platforms:', responseData.error_msg)
        showError(`Failed to load connected platforms: ${responseData.error_msg}`)
        return
      }

      if (!responseData.success) {
        console.error('Error fetching connected platforms:', responseData.error_msg)
        showError(`Failed to load connected platforms: ${responseData.error_msg}`)
        return
      }

      const connectedPlatformsData = responseData.body.connectedPlatforms || []

      // If no connected platforms, redirect to connect page
      if (connectedPlatformsData.length === 0) {
        window.location.href = '/dashboard/connect';
        return;
      }

      // Set connected platforms state based on API response
      const newConnectedPlatforms = {
        facebook: false,
        instagram: false,
        telegram: false,
        web: false
      }

      // Build credentials object for display
      const newCredentials: any = {
        id: 0,
        client_id: '',
        fb_name: null,
        ig_name: null,
        tg_name: null,
        web_name: null,
        web_domain: null,
        tg_connection_type: null
      }

      // Populate from connected platforms data
      connectedPlatformsData.forEach((platform: any) => {
        newConnectedPlatforms[platform.platform as keyof typeof newConnectedPlatforms] = true
        
        switch (platform.platform) {
          case 'facebook':
            newCredentials.fb_name = platform.displayName
            break
          case 'instagram':
            newCredentials.ig_name = platform.displayName
            break
          case 'telegram':
            newCredentials.tg_name = platform.displayName
            newCredentials.tg_connection_type = platform.connectionType === 'bot' ? 'Bot' : 'Business'
            break
          case 'web':
            newCredentials.web_domain = platform.displayName
            break
        }
      })

      setConnectedPlatforms(newConnectedPlatforms)
      setCredentials(newCredentials)

    } catch (error) {
      console.error('Unexpected error in fetchClientCredentials:', error)
      showError('An unexpected error occurred. Please try again later.')
    } finally {
      setIsLoading(false)
    }
  }

  // Note: Removed webhook copy and edit mode functions since this page only handles disconnection

  // Show delete confirmation dialog
  const initiateDelete = (platform: string) => {
    // Create a mock ID for the platform (we'll use hash of platform name)
    const platformId = platform.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    setDeleteConfirm({
      id: platformId,
      platform
    });
  };

  // Handle platform deletion after confirmation
  const handleConfirmDelete = async () => {
    if (!deleteConfirm?.platform) return;
    
    const platform = deleteConfirm.platform;
    try {
      // Set deleting state
      setIsDeleting(true);

      /*
      // OLD APPROACH: Call Next.js API that handles everything
      const response = await fetch('/api/platform/disconnect-secure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: platform
        }),
      });
      */

      // NEW APPROACH: Call Next.js API for auth, then delegate to external workflow
      const response = await fetch('/api/platform/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: platform
        }),
      });

      // Wait for a minimum of 1 second for UX purposes
      await new Promise(resolve => setTimeout(resolve, 1000));

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error_msg || `Failed to disconnect ${platform}`);
      }

      if (!responseData.success) {
        throw new Error(responseData.error_msg || `Unexpected response from server when disconnecting ${platform}`);
      }

      // All database operations, cache updates, and external webhook calls are now handled by external workflow

      // Update connected platforms state
      setConnectedPlatforms(prev => ({
        ...prev,
        [platform]: false
      }));

      // Show success message (showSuccess already handles auto-dismiss after 3 seconds)
      showSuccess(`Successfully disconnected ${platform}!`);

    } catch (error) {
      console.error(`Error disconnecting ${platform}:`, error);
      showError(`Failed to disconnect ${platform}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      // Clear error message after 3 seconds
      setTimeout(() => {
        hideOverlay();
      }, 3000);
    } finally {
      // Reset states
      setIsDeleting(false);
      setDeleteConfirm(null);
    }
  };

  // Handle cancel delete confirmation
  const handleCancelDelete = () => {
    setDeleteConfirm(null);
  };

  // Note: Removed unused connect functions since this is an edit page for disconnection only

  // Don't render anything server-side
  if (!isClient) return null

  // Platform data structure for PlatformCard component
  const platformsData = [
    {
      id: 'facebook',
      name: t('facebook_messenger'),
      icon: <FaFacebookMessenger size={18} className="text-blue-500" />,
      displayName: credentials?.fb_name || t('not_set'),
      isConnected: connectedPlatforms.facebook,
      isEnabled: true // Not used in disconnect mode
    },
    {
      id: 'instagram',
      name: t('instagram'),
      icon: <FaInstagram size={18} className="text-pink-500" />,
      displayName: credentials?.ig_name || t('not_set'),
      isConnected: connectedPlatforms.instagram,
      isEnabled: true // Not used in disconnect mode
    },
    {
      id: 'telegram',
      name: `${t('telegram')} (${credentials?.tg_connection_type || 'Bot'})`,
      icon: <FaPaperPlane size={16} className="text-blue-400" />,
      displayName: credentials?.tg_name || t('not_set'),
      isConnected: connectedPlatforms.telegram,
      isEnabled: true // Not used in disconnect mode
    },
    {
      id: 'web',
      name: t('web_api'),
      icon: <FaGlobe size={18} className="text-purple-500" />,
      displayName: credentials?.web_domain || t('not_set'),
      isConnected: connectedPlatforms.web,
      isEnabled: true // Not used in disconnect mode
    }
  ]

  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects */}
      {themeConfig.backgroundEffects}

      {/* Theme-aware Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.card} rounded-2xl px-4 py-3 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >
            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <Image
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  width={32}
                  height={32}
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-2">
        {/* Main content animations commented out as requested */}
        <div>
          {/* Content header with title and back button */}
          <DashboardHeader 
            backHref="/dashboard/connect"
            titleKey="edit_connections"
          />

          {/* Loading Indicator */}
          {isLoading && (
            <div className={`mb-6 p-4 ${theme === 'dark' ? themeConfig.secondCard : 'bg-jade-purple-light'} border border-jade-purple/30 rounded-lg ${themeConfig.text} flex items-center`}>
              <svg className={`animate-spin -ml-1 mr-3 h-5 w-5 ${themeConfig.text}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="text-sm font-body">{t('loading_connection_details')}</p>
            </div>
          )}

          {/* Status Overlay - Unified error and success messages */}
          <UpdateStatusOverlay
            updateStatus={overlayStatus}
            updateProgress={0}
            updateMessage={overlayMessage}
            onClose={() => setOverlayStatus('idle')}
          />

          {/* Delete Confirmation Modal */}
          <DeleteConfirmationModal
            deleteConfirm={deleteConfirm}
            isDeleting={isDeleting}
            onCancel={handleCancelDelete}
            onConfirmDelete={handleConfirmDelete}
          />

          {/* Connected Platforms Section */}
          {!isLoading && (
            <div className="space-y-4">
              {/* Render only connected platforms using PlatformCard component */}
              {platformsData
                .filter((platform) => platform.isConnected) // Only show connected platforms
                .map((platform) => (
                  <PlatformCard
                    key={platform.id}
                    platform={platform.id}
                    name={platform.name}
                    icon={platform.icon}
                    displayName={platform.displayName}
                    isConnected={platform.isConnected}
                    isEnabled={platform.isEnabled}
                    onToggle={() => {}} // Not used in disconnect mode
                    mode="disconnect"
                    onDisconnect={initiateDelete}
                    isDeleting={isDeleting}
                    backgroundColor={themeConfig.secondCard}
                  />
                ))}

              {/* No connected platforms message */}
              {platformsData.filter((platform) => platform.isConnected).length === 0 && (
                <div className={`${themeConfig.card} border ${themeConfig.border} rounded-lg p-6 text-center`}>
                  <p className={`${themeConfig.textMuted} font-body`}>{t('no_platforms_connected_edit')}</p>
                  <Link href="/dashboard/connect" className="mt-4 inline-block bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors font-body">
                    {t('connect_platforms')}
                  </Link>
                </div>
              )}
            </div>
          )}

          {/* <div className="mt-8 flex justify-center">
            <Link href="/dashboard/connect" className="bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors font-body">
              Back to Connections
            </Link>
          </div> */}
        </div>
      </div>
    </div>
  )
}