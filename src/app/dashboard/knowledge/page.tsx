'use client'

import { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useDashboardData, triggerDashboardRefresh } from '@/hooks/useOptimizedData';
import { FaBrain, FaMicrophone } from 'react-icons/fa';
import { useLanguage } from '@/context/LanguageContext';
import { useTheme, useThemeConfig } from '@/context/ThemeContext';
import { motion } from 'framer-motion';
import { Button, LinkButton, DashboardHeader } from '@/components/ui'; // Import our new Button components
import LanguageSwitcher from '@/components/LanguageSwitcher';
import DashboardFooter from '@/components/DashboardFooter';
import AudioPlayer from '@/components/ui/AudioPlayer';
import { 
  EditModal, 
  UpdateConfirmationModal, 
  DeleteConfirmationModal, 
  AudioModeWarningModal, 
  DeleteAudioConfirmationModal
} from '@/components/ui/modals';
import KnowledgeTopSection from '@/components/ui/knowledge/KnowledgeTopSection';
import HelpSection from '@/components/ui/knowledge/HelpSection';
import { UpdateStatusOverlay } from '@/components/ui/knowledge';


// Define the type for uploaded PDFs including the File object
// type UploadedPdf = {
//   id: number;
//   name: string;
//   size: string;
//   date: string;
//   file: File; // Add the File object
// };


// Define the type for editing item
interface EditItem {
  id: number;
  field: 'question' | 'answer';
  value: string;
}


export default function KnowledgePage() {
  const router = useRouter()
  const pathname = usePathname()
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Use unified dashboard cache for all knowledge page data
  const { 
    data: dashboardData, 
    loading: isDashboardLoading
  } = useDashboardData()
  const clientLang = dashboardData?.clientInfo?.lang
  const knowledgeStats = dashboardData?.knowledgeStats

  const [isUploading, setIsUploading] = useState(false)
  const [question, setQuestion] = useState<string>('')
  const [answer, setAnswer] = useState<string>('')
  const [isAudioAnswer, setIsAudioAnswer] = useState<boolean>(false)
  const [isValidatingAudio, setIsValidatingAudio] = useState<boolean>(false)
  const [audioValidation, setAudioValidation] = useState<{valid: boolean, duration?: number, file_id?: string, previewUrl?: string, error?: string} | null>(null)
  const [recentQA, setRecentQA] = useState<Array<{
    id: number,
    question: string,
    answer: string,
    isAudioAnswer?: boolean,
    audioDuration?: number,
    audioFileId?: string,
    processedAudioBlob?: Blob
  }>>([])
  const [isUpdating, setIsUpdating] = useState(false)
  const [editingItem, setEditingItem] = useState<EditItem | null>(null)
  const [hasFocusedInput, setHasFocusedInput] = useState<boolean>(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<number | null>(null)
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [showAudioModeWarning, setShowAudioModeWarning] = useState(false)
  const [showAudioDeleteConfirm, setShowAudioDeleteConfirm] = useState(false)
  
  // Audio playback state for preview section
  const [selectedPreviewAudioId, setSelectedPreviewAudioId] = useState<string | null>(null)
  const [isPreviewAudioPlaying, setIsPreviewAudioPlaying] = useState(false)
  
  // Audio playback state for Recently Added section
  const [recentQAPreviewUrls, setRecentQAPreviewUrls] = useState<Map<number, string>>(new Map())
  const [selectedRecentAudioId, setSelectedRecentAudioId] = useState<number | null>(null)
  const [isRecentAudioPlaying, setIsRecentAudioPlaying] = useState(false)

  // Get knowledge stats from dashboard cache instead of separate state
  const totalFaqs = knowledgeStats?.faqCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0
  // Photo stats removed - no longer needed
  const photoCount = 0
  const photoLimit = 0
  const photoUsagePercentage = 0
  const isLoadingCount = isDashboardLoading


  const questionInputRef = useRef<HTMLInputElement>(null)
  const answerInputRef = useRef<HTMLInputElement>(null)

  // Viewing popup state


  // Remove PDF upload state
  // const [uploadedPdfs, setUploadedPdfs] = useState<Array<{id: number, name: string, size: string, date: string, file: File}>>([]);
  // const fileInputRef = useRef<HTMLInputElement>(null)
  // const [dragActive, setDragActive] = useState(false)

  // Remove state for document saving
  // const [isDocUpdating, setIsDocUpdating] = useState(false)
  // const [showDocConfirmation, setShowDocConfirmation] = useState(false)
  // const [docUpdateStatus, setDocUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  // const [docUpdateMessage, setDocUpdateMessage] = useState('')
  // const [docUpdateProgress, setDocUpdateProgress] = useState(0)



  // Memoize the handleSaveEdit function
  const handleSaveEdit = useCallback(() => {
    // Use requestAnimationFrame for smoother UI updates
    requestAnimationFrame(() => {
      if (!editingItem) {
        return;
      }

      // Batch state updates
      if (editingItem.id === -1) {
        setQuestion(editingItem.value);
      } else if (editingItem.id === -2) {
        setAnswer(editingItem.value);
        // If this is an answer edit and we're in audio mode, validate the new code
        if (isAudioAnswer && editingItem.value.trim()) {
          validateAudioCode(editingItem.value.trim());
        }
      } else {
        // Use functional update to avoid dependency on recentQA
        setRecentQA(prev =>
          prev.map(qa =>
            qa.id === editingItem.id
              ? { ...qa, [editingItem.field]: editingItem.value }
              : qa
          )
        );
      }

      // Close modal after state updates
      setEditingItem(null);
      setHasFocusedInput(false);
      
    });
  }, [editingItem, isAudioAnswer]);







  // Add effect to focus textarea and set cursor at the end when editing
  // Note: This is now handled by the EditModal component
  useEffect(() => {
    // This effect is kept for compatibility but the actual focus logic 
    // is now handled within the EditModal component
  }, [editingItem, hasFocusedInput])

  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);



  // Function to focus input and set cursor at the end
  const focusInputAtEnd = (ref: React.RefObject<HTMLInputElement>) => {
    if (ref.current) {
      ref.current.focus()
      const length = ref.current.value.length
      ref.current.setSelectionRange(length, length)
    }
  }

  const handleStartEdit = (id: number, field: 'question' | 'answer', value: string) => {
    setEditingItem({ id, field, value })
    setHasFocusedInput(false)
    // Focus and set cursor at end will happen in useEffect
  }

  // handleSaveEdit is now defined above with useCallback


  const knowledgeItems = [
    {
      id: 1,
      title: 'Company FAQ',
      itemCount: 24,
      dateAdded: 'Apr 12, 2025',
      isActive: true
    },
    {
      id: 2,
      title: 'Product Information',
      itemCount: 36,
      dateAdded: 'Apr 10, 2025',
      isActive: true
    },
    {
      id: 3,
      title: 'Return Policy',
      itemCount: 8,
      dateAdded: 'Apr 5, 2025',
      isActive: false
    }
  ]

  // Function to validate audio code
  const validateAudioCode = async (audioCode: string) => {
    if (!audioCode.trim()) {
      setAudioValidation(null)
      return
    }

    setIsValidatingAudio(true)
    try {
      const response = await fetch('/api/audio/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ audioCode: audioCode.trim() })
      })

      if (response.ok && response.headers.get('X-Audio-Valid') === 'true') {
        // Response is processed audio blob
        const audioBlob = await response.blob()
        const previewUrl = URL.createObjectURL(audioBlob)
        
        const duration = parseInt(response.headers.get('X-Audio-Duration') || '0')
        const fileId = response.headers.get('X-Audio-FileId') || ''
        
        if (duration > 0) {
          setAudioValidation({
            valid: true,
            duration: duration,
            file_id: fileId,
            previewUrl: previewUrl
          })
        } else {
          // Invalid duration
          URL.revokeObjectURL(previewUrl)
          setAudioValidation(null)
          setAnswer('')
          setUpdateMessage(t('audio_code_incorrect'))
          setUpdateStatus('error')
          setTimeout(() => setUpdateStatus('idle'), 2000)
        }
      } else {
        // Handle JSON error response
        const errorData = await response.json()
        console.error('Audio validation failed:', errorData.error_msg)
        
        // Clear back to normal audio mode
        setAudioValidation(null)
        setAnswer('')
        setUpdateMessage(t('audio_code_incorrect'))
        setUpdateStatus('error')
        setTimeout(() => setUpdateStatus('idle'), 2000)
      }
    } catch (error) {
      console.error('Audio validation error:', error)
      // Clear back to normal audio mode on error
      setAudioValidation(null)
      setAnswer('')
      // Show error message for 2000ms
      setUpdateMessage(t('audio_code_incorrect'))
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 2000)
    } finally {
      setIsValidatingAudio(false)
    }
  }

  // Audio playback handlers for preview section
  const handlePreviewAudioClick = (previewUrl: string) => {
    const audioId = 'preview'
    // Reset Recently Added audio when playing preview
    setSelectedRecentAudioId(null)
    setIsRecentAudioPlaying(false)
    
    if (selectedPreviewAudioId === audioId) {
      // Same audio clicked - toggle play/pause
      setIsPreviewAudioPlaying(!isPreviewAudioPlaying)
    } else {
      // Different audio clicked - select new one and start playing
      setSelectedPreviewAudioId(audioId)
      setIsPreviewAudioPlaying(true)
    }
  }

  // Audio playback handlers for Recently Added section
  const handleRecentAudioClick = (id: number) => {
    // Reset preview audio when playing recent audio
    setSelectedPreviewAudioId(null)
    setIsPreviewAudioPlaying(false)
    
    if (selectedRecentAudioId === id) {
      // Same audio clicked - toggle play/pause
      setIsRecentAudioPlaying(!isRecentAudioPlaying)
    } else {
      // Different audio clicked - select new one and start playing
      setSelectedRecentAudioId(id)
      setIsRecentAudioPlaying(true)
    }
  }

  // Create preview URLs for Recently Added audio items
  useEffect(() => {
    const newUrls = new Map<number, string>()
    
    recentQA.forEach(qa => {
      if (qa.processedAudioBlob) {
        const previewUrl = URL.createObjectURL(qa.processedAudioBlob)
        newUrls.set(qa.id, previewUrl)
      }
    })
    
    // Clean up old URLs
    recentQAPreviewUrls.forEach((url, id) => {
      if (!newUrls.has(id)) {
        URL.revokeObjectURL(url)
      }
    })
    
    setRecentQAPreviewUrls(newUrls)
    
    // Cleanup on unmount
    return () => {
      newUrls.forEach(url => URL.revokeObjectURL(url))
    }
  }, [recentQA])

  // Handle audio delete confirmation
  const handleConfirmAudioDelete = () => {
    if (audioValidation?.previewUrl) {
      URL.revokeObjectURL(audioValidation.previewUrl)
    }
    setAudioValidation(null)
    setAnswer('')
    setShowAudioDeleteConfirm(false)
  }

  const handleAddQA = async () => {
    if (question.trim() && answer.trim()) {
      // For audio answers, check if validation passed
      if (isAudioAnswer && (!audioValidation?.valid)) {
        setUpdateMessage('Please provide a valid audio code')
        setUpdateStatus('error')
        setTimeout(() => setUpdateStatus('idle'), 3000)
        return
      }

      // Get processed audio blob for storage
      let processedAudioBlob: Blob | undefined
      if (isAudioAnswer && audioValidation?.valid && audioValidation.previewUrl) {
        try {
          // Convert preview URL back to blob for storage
          const response = await fetch(audioValidation.previewUrl)
          processedAudioBlob = await response.blob()
        } catch (error) {
          console.error('Error converting preview URL to blob:', error)
        }
      }

      setRecentQA(prev => [...prev, {
        id: Date.now(),
        question: question.trim(),
        answer: answer.trim(),
        isAudioAnswer: isAudioAnswer,
        audioDuration: isAudioAnswer && audioValidation?.valid ? audioValidation.duration : undefined,
        audioFileId: isAudioAnswer && audioValidation?.valid ? audioValidation.file_id : undefined,
        processedAudioBlob: processedAudioBlob
      }]);

      // Reset fields after adding
      setQuestion('');
      setAnswer('');
      // Reset audio validation but keep audio mode
      if (audioValidation?.previewUrl) {
        URL.revokeObjectURL(audioValidation.previewUrl);
      }
      setAudioValidation(null);
      
    }
  }

  // Prompt for confirmation before update
  const handleUpdate = () => {
    if (recentQA.length === 0) {
      setUpdateMessage(t('no_questions_update'))
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }
    if (recentQA.length > 10) {
      setUpdateMessage('Maximum 10 FAQs per update. Please remove some items.')
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }
    setShowConfirmation(true)
  }

  // Note: blobToBase64 function is defined below in saveQAToSupabase

  // Helper function to convert blob to base64
  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onerror = reject;
      reader.onload = () => {
        const base64String = reader.result as string;
        resolve(base64String.split(',')[1]); // Remove the data URL prefix
      };
      reader.readAsDataURL(blob);
    });
  }

  // Save Q&A items using API route (which uses PostgreSQL webhook)
  const saveQAToSupabase = async () => {
    setShowConfirmation(false);
    setIsUpdating(true);
    setUpdateStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage(t('processing_additions'));

    const totalItems = recentQA.length;
    if (totalItems === 0) {
      setUpdateMessage(t('no_questions_update'));
      setUpdateStatus('error');
      setTimeout(() => setUpdateStatus('idle'), 3000);
      setIsUpdating(false);
      return;
    }

    // Start realistic progress animation (5 seconds to 90%)
    let progressStep = 0;
    const progressInterval = setInterval(() => {
      setUpdateProgress(prev => {
        if (prev >= 90) return prev; // Stop at 90% until real response
        
        progressStep++;
        // Realistic progress curve: slow start, fast middle, slow end
        if (progressStep <= 4) return prev + 3; // 0-12%: slow start (0.8s)
        if (progressStep <= 12) return prev + 6; // 12-60%: fast processing (1.6s)  
        if (progressStep <= 20) return prev + 3; // 60-84%: slow end (1.6s)
        return Math.min(90, prev + 1); // 84-90%: final stretch (1s)
      });
    }, 200); // 200ms intervals = smoother animation

    try {
      // Prepare knowledge items for API
      const knowledgeItems = []
      const audioCount = recentQA.filter(qa => qa.isAudioAnswer).length
      
      if (audioCount > 0) {
        setUpdateMessage(`Uploading ${audioCount} audio file${audioCount > 1 ? 's' : ''}...`);
      } else {
        setUpdateMessage('Preparing FAQs for upload...');
      }

      // Use FormData to send blobs
      const formData = new FormData()
      
      for (let i = 0; i < recentQA.length; i++) {
        const qa = recentQA[i]
        
        // Add FAQ data
        knowledgeItems.push({
          question: qa.question,
          answer: qa.answer,
          isAudioAnswer: qa.isAudioAnswer || false,
          audioFileId: qa.audioFileId,
          audioDuration: qa.audioDuration,
          // Add index reference for blob mapping
          audioIndex: qa.isAudioAnswer && qa.processedAudioBlob ? i : null
        });
        
        // Add audio blob if it exists
        if (qa.isAudioAnswer && qa.processedAudioBlob) {
          formData.append(`audioBlob_${i}`, qa.processedAudioBlob, `audio_${i}.m4a`)
        }
      }

      // Add FAQ batch as JSON string
      formData.append('faqBatch', JSON.stringify(knowledgeItems))
      
      // Add sector and lang from dashboard cache to eliminate API cache lookup
      if (dashboardData?.clientInfo?.sector) {
        formData.append('sector', dashboardData.clientInfo.sector)
      }
      if (dashboardData?.clientInfo?.lang) {
        formData.append('lang', dashboardData.clientInfo.lang)
      }

      // Show processing message
      if (audioCount > 0) {
        setUpdateMessage('Uploading audio files and saving to database...');
      } else {
        setUpdateMessage('Saving FAQs to database...');
      }

      // Call new batch API route
      const response = await fetch('/api/knowledge/add-batch', {
        method: 'POST',
        body: formData,
      });

      let responseData = await response.json();
      
      // Handle array response format from N8N webhook
      if (Array.isArray(responseData) && responseData.length > 0) {
        responseData = responseData[0]
      }

      if (!response.ok || (!responseData.success && !responseData.partial_success)) {
        throw new Error(responseData.error_msg || 'Failed to save knowledge items');
      }

      // Clear progress interval and smoothly fill to 100%
      clearInterval(progressInterval);
      
      // Function to handle success/error after animation completes
      const handleSuccessMessage = (responseData: any) => {
        // Handle partial success (some FAQs failed)
        if (responseData.partial_success) {
          const successCount = responseData.body?.items_processed || 0;
          const failedCount = responseData.body?.items_failed || 0;
          
          // Remove only the successful FAQs from the UI list (first N items)
          if (successCount > 0) {
            setRecentQA(prev => prev.slice(successCount));
          }

          // Trigger dashboard refresh to update FAQ count
          triggerDashboardRefresh();

          setUpdateStatus('error');
          setUpdateMessage(responseData.error_msg || `${successCount} FAQs saved successfully, ${failedCount} failed. Please retry the remaining items.`);

          setTimeout(() => {
            setUpdateStatus('idle');
            setIsUpdating(false);
          }, 3000); // Error timeout
        } else {
          // Full success - all FAQs processed successfully
          const count = recentQA.length;
          const plural = count !== 1 ? 's' : '';
          setUpdateMessage(t('update_success').replace('{count}', count.toString()).replace('{plural}', plural));
          setUpdateStatus('success');

          // Trigger dashboard refresh to update FAQ count
          triggerDashboardRefresh();

          // Clear the recent QA list
          setRecentQA([]);

          // Show success message and reset
          setTimeout(() => {
            setUpdateStatus('idle');
            setIsUpdating(false);
          }, 1500);
        }
      };
      
      // Smooth fill to 100% directly
      setTimeout(() => {
        setUpdateProgress(100);
        // Wait a bit at 100%, then show success
        setTimeout(() => {
          handleSuccessMessage(responseData);
        }, 300);
      }, 50);

    } catch (error: any) {
      console.error('Error updating knowledge base:', error);

      // Clear progress interval on error
      clearInterval(progressInterval);

      // Check for browser blocking errors
      if (error.name === 'AbortError' || 
          error.message?.includes('blocked') || 
          error.message?.includes('ERR_BLOCKED_BY_CLIENT') ||
          error.message?.includes('Failed to fetch')) {
        
        setUpdateStatus('error');
        setUpdateMessage('Request blocked by browser security. Please try: 1) Disable ad-blocker/shields for this site, 2) Use Chrome browser, or 3) Disable VPN temporarily.');
        
        setTimeout(() => {
          setUpdateStatus('idle');
          setIsUpdating(false);
        }, 5000); // Longer timeout for browser-specific message
        return;
      }

      // Check if this is a partial success (some FAQs succeeded, some failed)
      if (error.response && error.response.data?.partial_success) {
        const responseData = error.response.data;
        const successfulFaqIds = responseData.body?.successful_faq_ids || [];
        
        // Remove successful FAQs from the recent list
        if (successfulFaqIds.length > 0) {
          setRecentQA(prev => prev.filter(qa => {
            // Map the recent QA to find matching FAQ IDs (this is tricky since we don't store faq_id in recent)
            // For now, remove the first N successful items
            const successfulCount = successfulFaqIds.length;
            const index = prev.indexOf(qa);
            return index >= successfulCount; // Keep only the failed ones (last items)
          }));
        }

        // Trigger dashboard refresh to update FAQ count
        if (responseData.body?.items_processed > 0) {
          triggerDashboardRefresh();
        }

        setUpdateStatus('error');
        const errorSuccessCount = responseData.body?.items_processed || 0;
        setUpdateMessage(responseData.error_msg || `${errorSuccessCount} FAQs saved successfully, ${responseData.body?.items_failed || 0} failed. Please retry the remaining items.`);
      } else {
        setUpdateStatus('error');
        setUpdateMessage(error.message || 'Failed to save knowledge items');
      }

      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUpdating(false);
      }, 3000); // Error timeout
    }
  };

  const handleDelete = (id: number) => {
    // Show confirmation modal instead of deleting immediately
    setItemToDelete(id);
    setShowDeleteConfirmation(true);
  }

  // Function to handle actual deletion after confirmation
  const confirmDelete = () => {
    if (itemToDelete === null) return;

    const qaToDelete = recentQA.find(qa => qa.id === itemToDelete);

    // Clean up audio blob URL if this is an audio item
    if (qaToDelete?.isAudioAnswer && recentQAPreviewUrls.has(itemToDelete)) {
      const audioUrl = recentQAPreviewUrls.get(itemToDelete);
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    }

    // Remove item after confirmation
    setRecentQA(prev => prev.filter(qa => qa.id !== itemToDelete));

    // Close the confirmation modal
    setShowDeleteConfirmation(false);
    setItemToDelete(null);
  }

  // Function to confirm switching to audio mode
  const confirmAudioMode = () => {
    setAnswer('');
    setIsAudioAnswer(true);
    setShowAudioModeWarning(false);
  }














  // Simple scroll disable when any popup is open
  useEffect(() => {
    const hasOpenModal = showConfirmation || editingItem || showDeleteConfirmation || showAudioModeWarning || showAudioDeleteConfirm || (updateStatus !== 'idle')

    if (hasOpenModal) {
      document.body.style.overflow = 'hidden'
      // Pause any playing audio when modal opens
      setSelectedPreviewAudioId(null)
      setIsPreviewAudioPlaying(false)
      setSelectedRecentAudioId(null)
      setIsRecentAudioPlaying(false)
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [showConfirmation, editingItem, showDeleteConfirmation, showAudioModeWarning, showAudioDeleteConfirm, updateStatus])

  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects */}
      {themeConfig.backgroundEffects}

      {/* Theme-aware Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.card} rounded-2xl px-4 py-3 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >
            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-2">
        <motion.div
          initial={{ opacity: 0, y: 4 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, ease: 'easeOut' }}
        >
          {/* Content header with title and back button */}
          <DashboardHeader 
            backHref="/dashboard"
            titleKey="ai_brain"
          />

          {/* Top Section */}
          <KnowledgeTopSection
            currentPath={pathname}
            totalFaqs={totalFaqs}
            totalFaqsLimit={totalFaqsLimit}
            photoCount={photoCount}
            photoLimit={photoLimit}
            faqUsagePercentage={faqUsagePercentage}
            photoUsagePercentage={photoUsagePercentage}
            isLoadingCount={isLoadingCount}
          />

          {/* Business Insights Section */}
          <div
            className={`relative ${themeConfig.card} rounded-2xl p-6 mb-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >

            <div className="relative z-10">
            <div className="flex justify-between items-center mb-6">
              <h2 className={`text-xl font-bold font-title ${themeConfig.text}`}>{t('business_insights')}</h2>
              <LinkButton
                href="/dashboard/knowledge/knowledgeBase"
                variant="secondary"
                size="sm"
                className="text-xs sm:text-base p-2 px-3 border-2"
              >
                {t('manage')}
              </LinkButton>
            </div>
            {/* <p className={`${themeConfig.textSecondary} text-sm mb-4 md:mb-6 font-body`}>
              {t('add_business_info')}
            </p> */}

            {/* Help Section */}
            <HelpSection 
              youtubeUrl="#" // TODO: Replace with actual YouTube URL
              message={t('add_business_info')}
              clientLang={clientLang || 'English'}
            />


            <div className="grid grid-cols-1 sm:grid-cols-12 gap-3 mb-6">
              {/* Question Trigger */}
              <div className="sm:col-span-5">
                <div
                  className={`px-2 md:px-4 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderActive} cursor-pointer ${themeConfig.borderHover} flex items-center min-h-[42px]`}
                  onClick={() => {
                    setEditingItem({
                      id: -1,
                      field: 'question',
                      value: question
                    });
                  }}
                >
                  {question ?
                    <span className="truncate font-body">{question}</span> :
                    <span className={`${themeConfig.textMuted} truncate font-body`}>{t('enter_question')}</span>
                  }
                </div>
              </div>
              {/* Answer Trigger */}
              <div className="sm:col-span-5 relative">
                <div
                  className={`px-2 pr-10 md:px-4 md:pr-10 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} focus:outline-none ${themeConfig.borderActive} ${audioValidation?.valid ? 'cursor-default' : `cursor-pointer ${themeConfig.borderHover}`} flex items-center min-h-[42px] overflow-hidden min-w-0`}
                  onClick={audioValidation?.valid ? undefined : () => {
                    setEditingItem({
                      id: -2,
                      field: 'answer',
                      value: answer
                    });
                  }}
                >
                  {/* Regular input display */}
                  {answer ? (
                    <div className="flex items-center w-full">
                      {isAudioAnswer && audioValidation?.valid && audioValidation.previewUrl ? (
                        <AudioPlayer
                          audioUrl={audioValidation.previewUrl}
                          compact={true}
                          className="mr-2"
                          isSelected={selectedPreviewAudioId === 'preview'}
                          isPlaying={selectedPreviewAudioId === 'preview' && isPreviewAudioPlaying}
                          onClick={() => handlePreviewAudioClick(audioValidation.previewUrl!)}
                        />
                      ) : isAudioAnswer && audioValidation?.valid ? (
                        <div className="flex items-center mr-2 flex-shrink-0">
                          <svg 
                            className={`w-4 h-4 mr-1 ${themeConfig.text}`}
                            fill="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <rect x="2" y="8" width="2" height="8" rx="1"/>
                            <rect x="6" y="5" width="2" height="14" rx="1"/>
                            <rect x="10" y="10" width="2" height="4" rx="1"/>
                            <rect x="14" y="3" width="2" height="18" rx="1"/>
                            <rect x="18" y="7" width="2" height="10" rx="1"/>
                            <rect x="22" y="12" width="2" height="2" rx="1"/>
                          </svg>
                          <span className={`text-sm ${themeConfig.textMuted}`}>
                            {audioValidation.duration}s
                          </span>
                        </div>
                      ) : isValidatingAudio ? (
                        <div className="mr-2 w-4 h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin flex-shrink-0"></div>
                      ) : null}
                      <span className="truncate flex-1 min-w-0 font-body">{isAudioAnswer && (audioValidation?.valid || isValidatingAudio) ? '' : answer}</span>
                    </div>
                  ) : (
                    <span className={`${themeConfig.textMuted} truncate pr-12 min-w-0 font-body`}>
                      {isAudioAnswer ? t('paste_audio_code') : t('enter_reply')}
                    </span>
                  )}
                  
                  {/* Microphone/Trash Icon */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent triggering the edit modal
                      
                      // Prevent clicks during audio validation
                      if (isValidatingAudio) {
                        return;
                      }
                      
                      if (isAudioAnswer && audioValidation?.valid) {
                        // Show confirmation before clearing audio
                        setShowAudioDeleteConfirm(true);
                      } else if (!isAudioAnswer) {
                        // Check if there's existing text
                        if (answer.trim()) {
                          setShowAudioModeWarning(true);
                        } else {
                          setIsAudioAnswer(true);
                        }
                      } else {
                        setIsAudioAnswer(false);
                      }
                    }}
                    disabled={isValidatingAudio}
                    className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full transition-all duration-200 ${
                      isValidatingAudio
                        ? 'opacity-50 cursor-not-allowed'
                        : isAudioAnswer && audioValidation?.valid
                          ? 'bg-red-500/20 text-red-500 hover:bg-red-500/30'
                          : isAudioAnswer 
                            ? 'bg-jade-purple-dark text-white hover:bg-jade-purple-dark/80' 
                            : `${themeConfig.textMuted} hover:text-jade-purple hover:bg-jade-purple/10`
                    }`}
                    title={
                      isValidatingAudio
                        ? 'Processing audio...'
                        : isAudioAnswer && audioValidation?.valid 
                          ? 'Clear audio code'
                          : isAudioAnswer 
                            ? 'Audio mode active - click to switch to text' 
                            : 'Switch to audio answer'
                    }
                  >
                    {isAudioAnswer && audioValidation?.valid ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    ) : (
                      <FaMicrophone className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
              {/* Add Button */}
              <div className="sm:col-span-2 flex items-center justify-center">
                <Button
                  onClick={handleAddQA}
                  variant="primary"
                  size="md"
                  className="text-xs sm:text-base w-full"
                  disabled={!question.trim() || !answer.trim() || (isAudioAnswer && (!audioValidation?.valid || isValidatingAudio))}
                >
                  {t('add')}
                </Button>
              </div>
            </div>

            {/* Recently Added Section - now part of the same container */}
            <div className={`flex justify-between items-center mb-3 border-t ${themeConfig.divider} pt-6`}>
              <h3 className={`text-lg font-semibold font-title ${themeConfig.text}`}>{t('recently_added')}</h3>
              <Button
                onClick={handleUpdate}
                variant="primary"
                size="md"
                className="text-xs sm:text-base"
                disabled={isUpdating || recentQA.length === 0}
              >
                {isUpdating ? t('updating') : t('update')}
              </Button>
            </div>

            {/* Column Headers */}
            <div className={`flex border-b ${themeConfig.divider} py-3 font-semibold ${themeConfig.textSecondary} font-body`}>
              <div className="w-[5%] px-2 text-left"></div>
              <div className="w-[35%] px-2 text-left">{t('question')}</div>
              <div className="w-[35%] px-2 text-left">{t('reply')}</div>
              <div className="w-[15%] px-2 text-left"></div>
              <div className="w-[10%] px-2 text-center"></div>
            </div>

            {/* List Items */}
            <div className="w-full">
              {recentQA.length > 0 ? (
                recentQA.map((qa, index) => (
                  <div key={qa.id} className={`flex border-b ${themeConfig.divider} py-3 items-center`}>
                    <div className={`w-[5%] px-2 text-left ${themeConfig.textMuted} font-body`}>
                      {index + 1}
                    </div>
                    <div
                      className="w-[35%] px-2 py-1"
                    >
                      <div
                        className={`h-full w-full truncate overflow-hidden min-w-0 px-2 py-1.5 ${themeConfig.secondCard} border-2 ${themeConfig.border} rounded-lg ${themeConfig.secondCardHover} flex items-center group cursor-pointer transition-all`}
                        title={qa.question}
                        onClick={() => handleStartEdit(qa.id, 'question', qa.question)}
                      >
                        <span className={`flex-1 truncate min-w-0 ${themeConfig.textSecondary} font-body`}>{qa.question}</span>
                        <svg className={`w-4 h-4 ml-2 ${themeConfig.textMuted} group-hover:text-jade-purple invisible group-hover:visible`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </div>
                    </div>
                    <div
                      className="w-[42.5%] px-2 py-1"
                    >
                      <div
                        className={`h-full w-full truncate overflow-hidden min-w-0 px-2 py-1.5 ${themeConfig.secondCard} border-2 ${themeConfig.border} rounded-lg ${themeConfig.secondCardHover} flex items-center group transition-all`}
                        title={qa.answer}
                        onClick={qa.isAudioAnswer ? undefined : () => handleStartEdit(qa.id, 'answer', qa.answer)}
                      >
                        {qa.isAudioAnswer ? (
                          recentQAPreviewUrls.has(qa.id) ? (
                            <AudioPlayer
                              audioUrl={recentQAPreviewUrls.get(qa.id)!}
                              compact={true}
                              isSelected={selectedRecentAudioId === qa.id}
                              isPlaying={selectedRecentAudioId === qa.id && isRecentAudioPlaying}
                              onClick={() => handleRecentAudioClick(qa.id)}
                            />
                          ) : (
                            <div className="flex items-center">
                              <svg 
                                className={`w-4 h-4 mr-1 ${themeConfig.text}`}
                                fill="currentColor" 
                                viewBox="0 0 24 24"
                              >
                                <rect x="2" y="8" width="2" height="8" rx="1"/>
                                <rect x="6" y="5" width="2" height="14" rx="1"/>
                                <rect x="10" y="10" width="2" height="4" rx="1"/>
                                <rect x="14" y="3" width="2" height="18" rx="1"/>
                                <rect x="18" y="7" width="2" height="10" rx="1"/>
                                <rect x="22" y="12" width="2" height="2" rx="1"/>
                              </svg>
                              <span className={`text-sm ${themeConfig.textMuted} flex-shrink-0`}>
                                {qa.audioDuration ? `${t('voice')} ${qa.audioDuration}s` : t('voice')}
                              </span>
                            </div>
                          )
                        ) : (
                          <>
                            <span className={`flex-1 truncate min-w-0 ${themeConfig.textSecondary} font-body`}>{qa.answer}</span>
                            <svg className={`w-4 h-4 ml-2 ${themeConfig.textMuted} group-hover:text-jade-purple invisible group-hover:visible`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="w-[10%] px-2 flex justify-center">
                      <button
                        className="p-2 rounded-full bg-red-500/20 hover:bg-red-500/30 text-red-500 transition-colors"
                        onClick={() => handleDelete(qa.id)}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className={`py-8 text-center ${themeConfig.textMuted}`}>
                  {t('no_questions_update')}
                </div>
              )}
            </div>
            </div>
          </div>

        </motion.div>
      </div>

      {/* FAQ Update Status Overlay */}
      <UpdateStatusOverlay
        updateStatus={updateStatus}
        updateProgress={updateProgress}
        updateMessage={updateMessage}
        onClose={() => setUpdateStatus('idle')}
        loadingText={`${t('updating')}...`}
        completeText={t('complete')}
        successText={t('success')}
        errorText={t('error')}
      />

      {/* Confirmation Modal */}
      <UpdateConfirmationModal
        showConfirmation={showConfirmation}
        onCancel={() => setShowConfirmation(false)}
        onConfirm={saveQAToSupabase}
        count={recentQA.length}
      />

      {/* Edit Modal */}
      <EditModal
        editingItem={editingItem}
        hasFocusedInput={hasFocusedInput}
        onValueChange={(value) => {
          setEditingItem(prev => prev ? {...prev, value} : null);
        }}
        onInputFocus={() => setHasFocusedInput(true)}
        onSave={handleSaveEdit}
        onClose={() => setEditingItem(null)}
      />


      <DashboardFooter />



      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        deleteConfirm={itemToDelete ? { id: itemToDelete } : null}
        isDeleting={false}
        onCancel={() => {
          setShowDeleteConfirmation(false);
          setItemToDelete(null);
        }}
        onConfirmDelete={confirmDelete}
      />

      {/* Audio Mode Warning Modal */}
      <AudioModeWarningModal
        showAudioModeWarning={showAudioModeWarning}
        onCancel={() => setShowAudioModeWarning(false)}
        onConfirm={confirmAudioMode}
      />

      {/* Audio Delete Confirmation Modal */}
      <DeleteAudioConfirmationModal
        showDeleteAudioConfirm={showAudioDeleteConfirm}
        onCancel={() => setShowAudioDeleteConfirm(false)}
        onConfirmDelete={handleConfirmAudioDelete}
      />

    </div>
  )
}