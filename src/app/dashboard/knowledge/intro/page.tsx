'use client'

import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { useDashboardProgress } from '@/hooks/useDashboardProgress'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui';
import { motion, AnimatePresence } from 'framer-motion'
import DashboardFooter from '@/components/DashboardFooter'
import DashboardTopSection from '@/components/ui/dashboard/DashboardTopSection';
import DashboardTopSectionSkeleton from '@/components/ui/dashboard/DashboardTopSectionSkeleton';
import HelpSection from '@/components/ui/knowledge/HelpSection';
import { UpdateStatusOverlay } from '@/components/ui/knowledge';
import { 
  EditModal,
  UpdateConfirmationModal,
  CancelConfirmationModal
} from '@/components/ui/modals';


import { useDashboardData } from '@/hooks/useOptimizedData'
import { FaComments } from 'react-icons/fa';

import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

// TypeScript interfaces
interface EditingItem {
  value: string
}



export default function IntroPage() {
  const pathname = usePathname()
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  
  // Use dashboard data for statistics and client info
  const { 
    data: dashboardData, 
    loading: isDashboardLoading,
  } = useDashboardData()
  const knowledgeStats = dashboardData?.knowledgeStats
  const clientLang = dashboardData?.clientInfo?.lang
  const clientInfo = dashboardData?.clientInfo || null

  // Subscription data for header
  const subscriptionData = useMemo(() => {
    return dashboardData?.clientInfo ? {
      plan_type: dashboardData.clientInfo.plan_type,
      next_billing_date: dashboardData.clientInfo.next_billing_date
    } : { plan_type: null, next_billing_date: null }
  }, [dashboardData?.clientInfo])

  // Format the date to a readable format (align with dashboard)
  const formattedBillingDate = useMemo(() => {
    const formatDate = (dateString: string | null) => {
      if (!dateString) return 'Not available';
      try {
        const utcDate = new Date(dateString);
        const localBillingDate = utcDate.toLocaleDateString('en-CA', { timeZone: 'Asia/Phnom_Penh' });
        const displayDate = new Date(localBillingDate);
        return displayDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      } catch (error) {
        console.error('Error formatting date:', error);
        return 'Invalid date';
      }
    };
    return formatDate(subscriptionData.next_billing_date);
  }, [subscriptionData.next_billing_date])

  // Get knowledge stats from dashboard data
  const totalFaqs = knowledgeStats?.faqCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0
  // Message usage data (align with dashboard)
  const usageData = dashboardData?.usageData || { usage_used: 0, usage_limit: 100, cmt_used: 0, cmt_limit: 2000 }

  

  // Comment stats will be provided by useDashboardProgress hook

  // Welcome data state (fetched fresh each time)
  const [welcomeData, setWelcomeData] = useState<any[]>([])
  const [isLoadingWelcome, setIsLoadingWelcome] = useState(true)

  // Fetch welcome data function
  const fetchWelcomeData = useCallback(async () => {
    try {
      setIsLoadingWelcome(true)
      
      const response = await fetch('/api/knowledge/welcome-chat')
      let responseData = await response.json()
      
      // Handle array response format from N8N webhook
      if (Array.isArray(responseData) && responseData.length > 0) {
        responseData = responseData[0]
      }
      
      if (!response.ok || !responseData.success) {
        console.error('Error fetching welcome data:', responseData.error_msg)
        setWelcomeData([])
        return
      }

      // Handle both array and single object responses
      const isArray = Array.isArray(responseData.body)
      const welcomeDataArray = isArray 
        ? responseData.body || []
        : responseData.body 
          ? [responseData.body] 
          : []
      
      setWelcomeData(welcomeDataArray)
    } catch (error) {
      console.error('Error fetching welcome data:', error)
      setWelcomeData([])
    } finally {
      setIsLoadingWelcome(false)
    }
  }, [])

  // Guard to avoid double-fetch in React 18 Strict Mode (dev)
  const hasFetchedWelcome = useRef(false)

  // Update welcome data in state after save
  const updateWelcomeDataInState = useCallback((updatedRecord: any) => {
    setWelcomeData(prevData => 
      prevData.map(record => 
        record.chat_id === updatedRecord.chat_id ? updatedRecord : record
      )
    )
  }, [])









  // Text content state
  const [introText, _setIntroText] = useState('')

  // Custom setters to track changes
  const setIntroText = (value: string) => {
    _setIntroText(value);
  }

  const [isIntroSaving, setIsIntroSaving] = useState(false)

  // Loading state for intro data
  const [isLoadingIntroData, setIsLoadingIntroData] = useState(true)

  // Helper function to check if all data is loaded (matching connect page pattern)
  const isDataReady = () => {
    return !isLoadingIntroData;
  }

  // Unified progress calculations
  const {
    messageUsagePercentage,
    commentUsagePercentage,
    isUsageLimitReached,
    messageUsed,
    messageLimit,
    commentUsed,
    commentLimit
  } = useDashboardProgress({
    usageData,
    isDashboardLoading,
    isPageDataLoading: isLoadingIntroData
  });

  // Header loading state management (matching connect page)
  const [showHeaderSkeleton, setShowHeaderSkeleton] = useState(false)
  useEffect(() => {
    let timer: ReturnType<typeof setTimeout> | null = null
    if (isDashboardLoading) {
      timer = setTimeout(() => setShowHeaderSkeleton(true), 80)
    } else {
      setShowHeaderSkeleton(false)
    }
    return () => { if (timer) clearTimeout(timer) }
  }, [isDashboardLoading])

  // Body reveal after header is ready and intro data is loaded (matching connect page)
  const [showBody, setShowBody] = useState(false)
  useEffect(() => {
    if (!isDashboardLoading && isDataReady()) {
      setShowBody(true)
    } else {
      setShowBody(false)
    }
  }, [isDashboardLoading, isLoadingIntroData])

  // Footer reveal after body is ready (matching connect page)
  const [showFooter, setShowFooter] = useState(false)
  useEffect(() => {
    if (showBody) {
      setShowFooter(true)
    } else {
      setShowFooter(false)
    }
  }, [showBody])

  // Edit mode state
  const [isIntroEditing, setIsIntroEditing] = useState(false)
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false)
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false)
  // Removed confirmationSection since we only have intro
  const [saveStatus, setSaveStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [updateMessage, setUpdateMessage] = useState('')

  // Initial state tracking for detecting changes
  const [initialIntroText, setInitialIntroText] = useState('')

  // Store actual chat_ids from database
  const [introChatId, setIntroChatId] = useState<string | null>(null)
  const [hasIntroChanges, setHasIntroChanges] = useState(false)
  

  // Editing state
  const [editingItem, setEditingItem] = useState<EditingItem | null>(null)

  // Track if textarea has been focused for mobile behavior
  const [hasFocusedInput, setHasFocusedInput] = useState(false)
  
  // Request deduplication for save operations
  const [isSaveInProgress, setIsSaveInProgress] = useState(false)

  // Change detection functions following the comprehensive rules
  const hasAnswerChanged = useCallback(() => {
    return introText.trim() !== initialIntroText.trim()
  }, [introText, initialIntroText])




  // Check if there are any real changes to save
  const hasRealChanges = useCallback(() => {
    return hasAnswerChanged()
  }, [hasAnswerChanged])






































  // Handle text input for intro
  const handleTextChange = (value: string) => {
    setIntroText(value);
    // Update change tracking
    setHasIntroChanges(
      value !== initialIntroText
    );
  };

  // Show save confirmation popup
  const showSaveConfirmationPopup = () => {
    // Check if there's valid content before showing the confirmation
    const hasValidContent = hasValidIntroContent();
    const hasChanges = hasIntroChanges;

    if (!hasValidContent) {
      console.warn(`Cannot save intro without text content`);
      return;
    }

    if (!hasChanges) {
      console.warn(`No changes to save for intro`);
      return;
    }

    setShowSaveConfirmation(true);
  };

  // Check if intro has changes
  const checkIntroChanges = (): boolean => {
    // Check if text has changed
    return introText !== initialIntroText;
  };



  // Check if intro has valid content (text only)
  const hasValidIntroContent = (): boolean => {
    return introText.trim() !== '';
  };

  // Disable page scroll when any popup/modal is open
  useEffect(() => {
    const hasAnyPopup = editingItem || showSaveConfirmation || showCancelConfirmation ||
                       saveStatus === 'loading' || saveStatus === 'success' || saveStatus === 'error';

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [editingItem, showSaveConfirmation, showCancelConfirmation, saveStatus]);




  // Reset intro to initial state
  const resetIntroToInitial = () => {
    // Reset text to initial value
    setIntroText(initialIntroText);
    setHasIntroChanges(false);
  };



  // Handle cancel edit
  const handleCancelEdit = () => {
    const hasChanges = checkIntroChanges();

    if (hasChanges) {
      // Show confirmation dialog
      setShowCancelConfirmation(true);
    } else {
      // No changes, just exit edit mode
      setIsIntroEditing(false);
    }
  };

  // Confirm cancel with reset
  const confirmCancel = () => {
    // Use the normal reset function
    resetIntroToInitial();
    setIsIntroEditing(false);

    // Close the confirmation dialog
    setShowCancelConfirmation(false);
  };





  // Handle edit button click
  const handleEdit = () => {
    // Save initial state for tracking changes
    setInitialIntroText(introText);
    setHasIntroChanges(false);
    setIsIntroEditing(true);
  };




  // Save intro settings to welcome_chat table
  const handleSave = async () => {
    // Prevent concurrent save operations
    if (isSaveInProgress) {
      console.warn('Save operation already in progress');
      return;
    }

    // Close the confirmation dialog
    setShowSaveConfirmation(false);

    // Set the appropriate saving state
    setIsSaveInProgress(true);
    setIsIntroSaving(true);
    setIsIntroEditing(false);


    // Set initial loading state
    setSaveStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage(`Preparing to save intro message...`);

    // Start realistic progress animation (3 seconds to 90%)
    let progressStep = 0;
    const progressInterval = setInterval(() => {
      setUpdateProgress(prev => {
        if (prev >= 90) return prev; // Stop at 90% until real response
        
        progressStep++;
        // Realistic progress curve: slow start, fast middle, slow end (3s total)
        if (progressStep <= 2) return prev + 6; // 0-12%: slow start (0.4s)
        if (progressStep <= 7) return prev + 10; // 12-62%: fast processing (1.0s)  
        if (progressStep <= 12) return prev + 4; // 62-82%: slow end (1.0s)
        return Math.min(90, prev + 2); // 82-90%: final stretch (0.6s)
      });
    }, 200); // 200ms intervals = smoother animation

    try {

      // Use the actual chat_id from database
      const chatId = introChatId;

      if (!chatId) {
        console.error(`No intro chat_id found. Record may not exist.`);
        setUpdateMessage(`Intro record not found. Please refresh the page.`);
        setSaveStatus('error');
        return;
      }

      // Validate that we have actual changes to save
      if (!hasRealChanges()) {
        setUpdateMessage('No changes to save');
        setSaveStatus('error');
        setTimeout(() => setSaveStatus('idle'), 2000);
        return;
      }

      setUpdateMessage('Processing changes...');



      // Prepare updateData with change flags (new pattern)
      const answerChanged = hasAnswerChanged()
      
      const updateData: Record<string, unknown> = {
        answerChanged: answerChanged
      };

      // Answer data (only if answer changed)
      if (answerChanged) {
        updateData.answer_p = introText
      }

      setUpdateMessage('Saving to database...');

      // Always update existing record (intro generated during registration)
      setUpdateMessage('Updating record...');
      try {
        // Prepare request body
        const requestBody = JSON.stringify({
          chat_id: chatId,
          updateData: updateData,
          sector: dashboardData?.clientInfo?.sector,
          lang: dashboardData?.clientInfo?.lang
        });
        const requestHeaders = { 'Content-Type': 'application/json' };

        const updateResponse = await fetch('/api/knowledge/welcome-chat', {
          method: 'PUT',
          headers: requestHeaders,
          body: requestBody
        });

        let updateResult = await updateResponse.json();
        
        // Handle array response format from N8N webhook
        if (Array.isArray(updateResult) && updateResult.length > 0) {
          updateResult = updateResult[0]
        }

        if (!updateResponse.ok) {
          throw new Error(updateResult.error || 'Failed to update record');
        }

        // Update local state with optimistic UI data for all changes
        if (updateWelcomeDataInState) {
          // Find current intro data
          const currentIntroData = welcomeData?.find((record: { chat_id: string }) => record.chat_id.endsWith('-1'));

          // Build updated record based on what changed
          const updatedRecord: any = {
            ...currentIntroData,
          };

          // Update answer fields if answer changed
          if (answerChanged) {
            updatedRecord.answer_p = introText // Set text
          }

          updateWelcomeDataInState(updatedRecord);
        }

        // Update local intro state to reflect the changes
      } catch (error) {
        console.error('Error updating record:', error);
        throw new Error('Failed to update record');
      }

      // Clear progress interval and smoothly fill to 100%
      clearInterval(progressInterval);
      
      // Smooth fill to 100% directly
      setTimeout(() => {
        setUpdateProgress(100);
        // Wait a bit at 100%, then show success
        setTimeout(() => {
          setUpdateMessage('Intro message saved!');
          setSaveStatus('success');
        }, 300);
      }, 50);

      // Reset initial state tracking to reflect the saved state
      setInitialIntroText(introText);
      setHasIntroChanges(false);

      // Auto-hide success message after 1.5 seconds
      setTimeout(() => setSaveStatus('idle'), 1500);

    } catch (error: unknown) {
      clearInterval(progressInterval);
      console.error('Error saving intro:', error);
      
      // Provide specific error messages based on error type
      let errorMessage = 'Error saving intro message. Please try again.'
      if (error instanceof Error) {
        if (error.message?.includes('Audio code')) {
          errorMessage = 'Invalid audio code. Please check the code and try again.'
        } else if (error.message?.includes('Unauthorized')) {
          errorMessage = 'Session expired. Please refresh the page and try again.'
        } else if (error.message?.includes('Network')) {
          errorMessage = 'Network error. Please check your connection and try again.'
        } else {
          errorMessage = error.message
        }
      }
      
      setUpdateMessage(errorMessage);
      setSaveStatus('error');
      
      // Auto-clear error status after 5 seconds
      setTimeout(() => {
        setSaveStatus('idle');
      }, 5000);
    } finally {
      // Reset the appropriate saving state
      setTimeout(() => {
        setIsIntroSaving(false);
        setIsSaveInProgress(false);
      }, 1000);
    }
  };

  // Load existing intro data from welcome data state
  const loadIntroData = useCallback(async () => {
    try {
      // Wait for welcome data to load
      if (isLoadingWelcome || welcomeData.length === 0) {
        return
      }

      // Set loading state
      setIsLoadingIntroData(true)

      // Get intro data based on chat_id pattern
      const introData = welcomeData?.find((record: { chat_id: string }) => record.chat_id.endsWith('-1'));

      // Set intro data if exists
      if (introData) {
        // Store the actual chat_id from database
        setIntroChatId(introData.chat_id);

        
        // Use answer_p field for text content
        const introTextValue = introData.answer_p || '';
        setIntroText(introTextValue);
        setInitialIntroText(introTextValue);
        



      }



      // Reset change tracking
      setHasIntroChanges(false);

    } catch (error) {
      console.error('Error loading intro data:', error);
    } finally {
      // Clear loading state
      setIsLoadingIntroData(false)
    }
  }, [isLoadingWelcome, welcomeData]);

  // Fetch welcome data on mount (guarded)
  useEffect(() => {
    if (hasFetchedWelcome.current) return
    hasFetchedWelcome.current = true
    fetchWelcomeData()
  }, [fetchWelcomeData])

  // Load intro data when welcome data is ready
  useEffect(() => {
    if (!isLoadingWelcome && welcomeData.length >= 0) {
      loadIntroData()
    }
  }, [isLoadingWelcome, welcomeData, loadIntroData])

  // Save handler for EditModal
  const handleSaveEdit = useCallback(() => {
    if (!editingItem) return;
    // Only save if the value actually changed from the initial value
    if (editingItem.value !== introText) {
      handleTextChange(editingItem.value);
    }
    // Close modal after state updates
    setEditingItem(null);
  }, [editingItem, introText])



  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);



  // Update change tracking whenever relevant data changes
  useEffect(() => {
    if (isIntroEditing) {
      setHasIntroChanges(checkIntroChanges());
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [introText, isIntroEditing, initialIntroText]);








  // Disable page scroll when any popup/modal is open
  useEffect(() => {
    const hasAnyPopup = editingItem || showSaveConfirmation || showCancelConfirmation ||
                       saveStatus === 'loading' || saveStatus === 'success' || saveStatus === 'error' ||
                       isLoadingIntroData;

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [editingItem, showSaveConfirmation, showCancelConfirmation, saveStatus,
      isLoadingIntroData]);

  

  return (
    <div className={themeConfig.pageBackground}>
      {/* Unified Dashboard header/top section with intent delay and crossfade */}
      <div className="min-h-[168px]">
        <AnimatePresence initial={false} mode="wait">
          {isDashboardLoading ? (
            showHeaderSkeleton && (
              <motion.div
                key="intro-header-skeleton"
                initial={{ opacity: 0, y: 4 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -4 }}
                transition={{ duration: 0.2, ease: 'easeOut' }}
              >
                <DashboardTopSectionSkeleton />
              </motion.div>
            )
          ) : (
            <motion.div
              key="intro-header"
              initial={{ opacity: 0, y: 4 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -4 }}
              transition={{ duration: 0.2, ease: 'easeOut' }}
            >
              <DashboardTopSection
                clientInfo={clientInfo}
                subscriptionData={subscriptionData}
                formattedBillingDate={formattedBillingDate}
                currentPath={pathname}
                totalFaqs={knowledgeStats?.faqCount || 0}
                totalFaqsLimit={knowledgeStats?.faqLimit || 0}
                photoCount={0}
                photoLimit={0}
                faqUsagePercentage={knowledgeStats?.faqUsagePercentage || 0}
                photoUsagePercentage={0}
                isLoadingCount={isDashboardLoading}
                messageUsed={messageUsed}
                messageLimit={messageLimit}
                messageUsagePercentage={messageUsagePercentage}
                commentUsed={commentUsed}
                commentLimit={commentLimit}
                commentUsagePercentage={commentUsagePercentage}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Intro Content */}
      <AnimatePresence>
        {showBody && (
          <motion.div
            key="intro-body"
            className="flex-grow container mx-auto px-4 py-2"
            initial={{ opacity: 0, y: 4 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -4 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
          >
              {/* Intro Section */}
              <div
                className={`relative ${themeConfig.card} rounded-2xl p-6 mb-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
                style={theme === 'dark' ? {
                  boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                } : {
                  boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
                }}
              >
                <div className="relative z-10">
                  <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center">
                      <FaComments className={`${themeConfig.text} mr-3 h-5 w-5`} />
                      <h2 className={`text-xl font-bold font-title ${themeConfig.text}`}>{t('intro_message')}</h2>
                    </div>
                    <div className="flex space-x-2">
                      {isIntroEditing ? (
                        <>
                          <Button
                            onClick={() => handleCancelEdit()}
                            variant="secondary"
                            size="sm"
                            className="text-xs sm:text-base"
                            disabled={isIntroSaving}
                          >
                            {t('cancel')}
                          </Button>
                          <div className="relative">
                            <Button
                              onClick={() => showSaveConfirmationPopup()}
                              variant="primary"
                              size="sm"
                              className="text-xs sm:text-base"
                              disabled={isIntroSaving || !hasIntroChanges || !hasValidIntroContent()}
                              title={!hasValidIntroContent() ? "Add text before saving" : (!hasIntroChanges ? "No changes to save" : "Save changes")}
                              isLoading={isIntroSaving}
                              loadingText={t('saving')}
                            >
                              {t('save')}
                            </Button>
                            {isIntroEditing && !hasValidIntroContent() && (
                              <div className={`absolute -bottom-6 right-0 text-xs ${themeConfig.errorText} whitespace-nowrap`}>
                                Add text
                              </div>
                            )}
                          </div>
                        </> 
                      ) : (
                        <Button
                          onClick={() => handleEdit()}
                          variant="secondary"
                          size="sm"
                          className="text-xs sm:text-base"
                        >
                          {t('edit')}
                        </Button>
                      )}
                    </div>
                  </div>
                  {/* Help Section */}
                  <HelpSection 
                    youtubeUrl="#" // TODO: Replace with actual YouTube URL
                    message={t('intro_description')}
                    clientLang={clientLang || 'English'}
                  />

                  {/* Answer Input (mirrors dashboard questions field UI) */}
                  <div className="mb-4">
                    <div
                      className={`px-2 md:px-4 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderActive} ${isIntroEditing ? `cursor-pointer ${themeConfig.borderHover}` : 'cursor-not-allowed opacity-70'} flex items-center min-h-[42px]`}
                      onClick={() => {
                        if (!isIntroEditing) return;
                        setEditingItem({
                          value: introText
                        });
                      }}
                    >
                      {introText ? (
                        <span className="truncate font-body">{introText}</span>
                      ) : (
                        <span className={`${themeConfig.textMuted} truncate font-body`}>{t('enter_welcome_message')}</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Edit modal*/}
              <EditModal
                editingItem={editingItem ? {
                  id: 1,
                  field: 'answer',
                  value: editingItem.value
                } : null}
                hasFocusedInput={hasFocusedInput}
                onValueChange={(value) => {
                  setEditingItem(prev => prev ? {...prev, value} : null);
                }}
                onInputFocus={() => setHasFocusedInput(true)}
                onSave={handleSaveEdit}
                onClose={() => setEditingItem(null)}
              />

              {/* Save Confirmation Modal */}
              <UpdateConfirmationModal
                showConfirmation={showSaveConfirmation}
                onCancel={() => setShowSaveConfirmation(false)}
                onConfirm={handleSave}
                title={t('save_intro_message')}
                message={t('save_intro_confirmation')}
              />

              {/* Cancel Confirmation Modal */}
              <CancelConfirmationModal
                showCancelConfirmation={showCancelConfirmation}
                onKeepEditing={() => setShowCancelConfirmation(false)}
                onConfirmDiscard={confirmCancel}
              />

              {/* Save Status Overlay */}
              <UpdateStatusOverlay
                updateStatus={saveStatus}
                updateProgress={updateProgress}
                updateMessage={updateMessage}
                onClose={() => setSaveStatus('idle')}
                loadingText={t('saving')}
                completeText={t('complete')}
                successText={t('success')}
                errorText={t('error')}
                spinnerSize="sm"
              />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Footer */}
      <AnimatePresence>
        {showFooter && (
          <motion.div
            key="intro-footer"
            initial={{ opacity: 0, y: 4 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -4 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
          >
            <DashboardFooter />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
