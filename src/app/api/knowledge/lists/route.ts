import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { v4 as uuidv4 } from 'uuid'
import { getM4AExtension } from '@/utils/audioConversion'
import { serverCache } from '@/lib/cache'
import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'
import { query, queryMany, queryOne } from '@/lib/postgres'

// R2 client configuration (for direct audio uploads)
const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
  forcePathStyle: true,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME!
const PUBLIC_URL = process.env.CLOUDFLARE_R2_PUBLIC_URL!

// Helper function to determine content type
const getContentType = (fileName: string): string => {
  const ext = fileName.toLowerCase().split('.').pop()
  switch (ext) {
    case 'm4a':
      return 'audio/mp4'
    case 'mp3':
      return 'audio/mpeg'
    case 'mp4':
      return 'audio/mp4'
    default:
      return 'audio/mp4'
  }
}

// Type definitions (for reference, not actively used)
// type KnowledgeItem = {
//   id: number
//   question_p: string
//   answer_p: string
//   created_at: string
//   updated_at: string
//   audio_url?: string
//   audio_duration?: number
//   photo_url?: unknown
//   photo_id?: string
//   audio_file_path?: string
//   auth_id: string
// }

// GET - Fetch all knowledge items for the client
export async function GET() {
  try {
    // Authentication check
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Fetch knowledge items directly from PostgreSQL
    const sql = `
      SELECT id, faq_id, question, question_p, answer_p, created_at, updated_at, 
             audio_url, audio_duration, photo_url, photo_id, audio_file_path
      FROM faqs 
      WHERE client_id = $1 AND is_visible = true 
      ORDER BY updated_at DESC
    `
    
    const data = await queryMany(sql, [clientId])

    // Return data as-is - frontend should use question_p/answer_p directly
    const processedData = data

    return NextResponse.json({ 
      success: true,
      body: processedData,
      error_msg: null
    })

  } catch (error) {
    console.error('Error in knowledge lists GET API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: 'Internal server error'
    }, { status: 500 })
  }
}


// DELETE - Delete a knowledge item
export async function DELETE(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get faq_id from URL search params
    const { searchParams } = new URL(request.url)
    const faq_id = searchParams.get('faq_id')

    if (!faq_id) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required parameter: faq_id'
      }, { status: 400 })
    }

    // Parse request body to get audio_file_path
    const body = await request.json()
    const { audio_file_path } = body

    // If FAQ has audio file, delete it from R2 before database deletion
    if (audio_file_path) {
      try {
        const deleteCommand = new DeleteObjectCommand({
          Bucket: BUCKET_NAME,
          Key: audio_file_path,
        })

        await r2Client.send(deleteCommand)
      } catch (cleanupError) {
        console.error('Error during audio cleanup:', cleanupError)
        // Don't fail the operation if cleanup fails
      }
    }

    // Delete the knowledge item(s) directly from PostgreSQL (handles multiple rows)
    const deleteSql = `
      DELETE FROM faqs 
      WHERE client_id = $1 AND faq_id = $2
    `
    
    const result = await query(deleteSql, [clientId, faq_id])

    if (result.rowCount === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Knowledge item not found or already deleted'
      }, { status: 404 })
    }

    // Update knowledge cache with decreased FAQ count (if cache exists)
    try {
      const knowledgeCacheKey = `knowledge_${authId}`
      const knowledgeData = serverCache.get(knowledgeCacheKey)
      
      if (knowledgeData) {
        // Update FAQ count in cache (subtract 1)
        const currentFaqCount = knowledgeData.knowledgeStats.faqCount || 0
        const newFaqCount = Math.max(currentFaqCount - 1, 0) // Ensure >= 0
        const faqLimit = knowledgeData.knowledgeStats.faqLimit || 1
        
        knowledgeData.knowledgeStats.faqCount = newFaqCount
        knowledgeData.knowledgeStats.faqUsagePercentage = Math.round((newFaqCount / faqLimit) * 100)
        
        serverCache.set(knowledgeCacheKey, knowledgeData, 30)
      }
    } catch (error) {
      console.warn('Could not update knowledge cache:', error)
    }

    // No webhook needed for DELETE operations

    return NextResponse.json({ 
      success: true,
      body: null, // No data needed for DELETE success
      error_msg: null
    })

  } catch (error) {
    console.error('Error in knowledge lists DELETE API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: 'Internal server error'
    }, { status: 500 })
  }
}

// PUT - Update a knowledge item (following intro management pattern)
export async function PUT(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Parse request body - handle both FormData (with audio blob) and JSON
    let faq_id: string, updateData: any, sectorFromRequest: string | null = null, langFromRequest: string | null = null, processedAudioBlob: File | null = null, questionFromRequest: string | null = null

    const contentType = request.headers.get('content-type')
    if (contentType?.includes('multipart/form-data')) {
      // FormData request with audio blob
      const formData = await request.formData()
      faq_id = formData.get('faq_id') as string
      updateData = JSON.parse(formData.get('updateData') as string)
      sectorFromRequest = formData.get('sector') as string | null
      langFromRequest = formData.get('lang') as string | null
      questionFromRequest = formData.get('question') as string | null
      processedAudioBlob = formData.get('processedAudioBlob') as File
    } else {
      // JSON request without audio blob
      const jsonData = await request.json()
      faq_id = jsonData.faq_id
      updateData = jsonData.updateData
      sectorFromRequest = jsonData.sector || null
      langFromRequest = jsonData.lang || null
      questionFromRequest = jsonData.question || null
    }

    if (!faq_id || !updateData) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required parameters: faq_id, updateData'
      }, { status: 400 })
    }

    // Step 3: Use client info from frontend request (with fallbacks like add-batch)
    let clientInfo = null

    if (sectorFromRequest || langFromRequest) {
      // Use sector/lang passed from frontend
      clientInfo = {
        sector: sectorFromRequest,
        lang: langFromRequest
      }
    } else {
      // Fallback 1: Try knowledge cache
      const knowledgeCacheKey = `knowledge_${authId}`
      const knowledgeData = serverCache.get(knowledgeCacheKey)

      if (knowledgeData) {
        clientInfo = {
          sector: knowledgeData.sector,
          lang: knowledgeData.clientLang
        }
      } else {
        // Fallback 2: Get client info from database
        try {
          const clientInfoSql = `
            SELECT sector, lang
            FROM clients
            WHERE auth_id = $1
          `
          const clientData = await queryOne(clientInfoSql, [authId])
          if (clientData) {
            clientInfo = {
              sector: clientData.sector,
              lang: clientData.lang
            }
          }
        } catch (error) {
          console.warn('Could not get client info from database:', error)
        }
      }
    }

    // Get webhook URL for background analytics
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL

    // Handle audio processing - upload to R2 AND convert to base64 for webhook
    let audioBase64: string | null = null
    if (processedAudioBlob && updateData.answerChanged && updateData.answer_p === null) {
      try {
        // Convert blob to buffer
        const arrayBuffer = await processedAudioBlob.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)
        
        // Convert to base64 for webhook (same pattern as add-batch)
        const blobToBase64 = async (blob: Blob): Promise<string> => {
          const arrayBuffer = await blob.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);
          return buffer.toString('base64');
        }
        
        audioBase64 = await blobToBase64(processedAudioBlob)
        
        // Generate audio ID and file path with timestamp + UUID
        const timestamp = Date.now()
        const uniqueId = uuidv4()
        const audioId = `${timestamp}-${uniqueId}`
        const fileExtension = getM4AExtension()
        const fileName = `${audioId}.${fileExtension}`
        const filePath = `audios/${authId}/${fileName}`
        
        const contentType = getContentType(fileName)
        
        // Upload to R2 for storage
        const putCommand = new PutObjectCommand({
          Bucket: BUCKET_NAME,
          Key: filePath,
          Body: new Uint8Array(buffer),
          ContentType: contentType,
          CacheControl: '3600',
          ContentDisposition: `inline; filename="${fileName}"`,
        })
        
        await r2Client.send(putCommand)
        
        // Generate public URL
        const publicUrl = `${PUBLIC_URL}/${filePath}`
        
        // Update the updateData with R2 information for database
        updateData.audio_url = publicUrl
        updateData.audio_file_path = filePath
        
        // Clean up old audio file if exists
        if (updateData.originalAudioFilePath) {
          const deleteCommand = new DeleteObjectCommand({
            Bucket: BUCKET_NAME,
            Key: updateData.originalAudioFilePath,
          })

          r2Client.send(deleteCommand).catch(cleanupError => {
            console.error('Background cleanup failed for old audio file:', updateData.originalAudioFilePath, cleanupError)
          })
        }
        
      } catch (audioError: unknown) {
        console.error('Error processing audio:', audioError)
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `Failed to process audio: ${audioError instanceof Error ? audioError.message : 'Unknown error'}`
        }, { status: 500 })
      }

    } else if (updateData.originalAudioFilePath && updateData.answerChanged && updateData.audio_url === null) {
      // Handle cleanup when switching from audio to text mode
      try {
        const deleteCommand = new DeleteObjectCommand({
          Bucket: BUCKET_NAME,
          Key: updateData.originalAudioFilePath,
        })

        await r2Client.send(deleteCommand)

        // Clear audio-related fields
        updateData.audio_url = null
        updateData.audio_file_path = null
      } catch (cleanupError) {
        console.error('Error during audio cleanup when switching to text mode:', cleanupError)
      }
    }

    // Store original FAQ data for rollback capability
    let originalFaq = null
    if (updateData.answerChanged || updateData.photoChanged || updateData.question_p !== undefined) {
      const originalFaqSql = `
        SELECT question_p, answer_p, audio_url, audio_file_path, photo_url, photo_id
        FROM faqs
        WHERE client_id = $1 AND faq_id = $2
      `
      originalFaq = await queryOne(originalFaqSql, [clientId, faq_id])
    }
    
    // Note: Old audio file cleanup moved to AFTER webhook success to prevent data loss on rollback


    // Prepare database update data based on change flags
    const dbUpdateData: Record<string, unknown> = {}

    // Handle photo changes
    if (updateData.photoChanged) {
      dbUpdateData.photo_url = updateData.photo_url
      dbUpdateData.photo_id = updateData.photo_id
      dbUpdateData.fb_photo_atmid = null
      dbUpdateData.tg_photo_atmid = null
      dbUpdateData.ig_photo_atmid = null
    }

    // Handle answer changes
    if (updateData.answerChanged) {
      dbUpdateData.fb_audio_atmid = null
      dbUpdateData.tg_audio_atmid = null
      dbUpdateData.ig_audio_atmid = null
      
      // Set answer fields based on what's provided
      if (updateData.answer_p !== undefined) {
        dbUpdateData.answer_p = updateData.answer_p
      }
      if (updateData.audio_url !== undefined) {
        dbUpdateData.audio_url = updateData.audio_url
      }
      if (updateData.audio_file_path !== undefined) {
        dbUpdateData.audio_file_path = updateData.audio_file_path
      }
      if (updateData.audio_duration !== undefined) {
        dbUpdateData.audio_duration = updateData.audio_duration
      }
      
    }

    // Handle question changes (independent of photo/answer)
    if (updateData.question_p !== undefined) {
      dbUpdateData.question_p = updateData.question_p
    }


    // Build dynamic update query
    const updateFields: string[] = []
    const updateValues: unknown[] = []
    let valueIndex = 3

    Object.entries(dbUpdateData).forEach(([key, value]) => {
      updateFields.push(`${key} = $${valueIndex}`)
      updateValues.push(value)
      valueIndex++
    })

    if (updateFields.length === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No fields to update'
      }, { status: 400 })
    }

    // Update FAQ(s) directly in PostgreSQL (handles multiple rows)
    const updateSql = `
      UPDATE faqs 
      SET ${updateFields.join(', ')}, updated_at = NOW()
      WHERE client_id = $1 AND faq_id = $2
    `
    
    const result = await query(updateSql, [clientId, faq_id, ...updateValues])
    
    if (result.rowCount === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'FAQ not found or update failed'
      }, { status: 404 })
    }

    // Wait for webhook response (only when answer changed)
    if (webhookUrl && updateData.answerChanged) {
      const jwtToken = generateWebhookToken()
      
      try {
        const webhookResponse = await fetch(webhookUrl, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwtToken}`
          },
          body: JSON.stringify({
            mode: 'faq',
            operation: 'update',
            conversation_id: uuidv4(), // Generated UUID for conversation tracking
            faq_id: faq_id,
            client_id: clientId,
            question: questionFromRequest || null,
            answer: updateData.answer_p || null,
            audio_url: audioBase64 || null, // Send base64 in audio_url field to webhook
            sector: clientInfo?.sector || null,
            lang: clientInfo?.lang || null
          })
        })

        if (!webhookResponse.ok) {
          throw new Error(`Webhook failed: ${webhookResponse.status}`)
        }

        // Handle response body from webhook
        const responseText = await webhookResponse.text()
        
        if (responseText.trim()) {
          try {
            const result = JSON.parse(responseText)
            
            // Check success field - handle both direct response and array response from N8N
            const responseData = Array.isArray(result) ? result[0] : result
            
            if (responseData && responseData.success === false) {
              throw new Error(`Webhook processing failed: ${responseData.error_msg || 'Unknown error'}`)
            }
          } catch (jsonError) {
            if (jsonError instanceof Error && jsonError.message && jsonError.message.startsWith('Webhook processing failed:')) {
              throw jsonError // Re-throw webhook failures
            }
            // Non-JSON response - treat as success
          }
        }
        // Empty response - treat as success
        
      } catch (webhookError) {
        console.error('Webhook failed for FAQ update:', webhookError)
        // Rollback: Revert database changes since webhook failed
        if (originalFaq) {
          try {
            console.log('Rolling back database changes due to webhook failure')
            
            // Build rollback query with original values
            const rollbackFields: string[] = []
            const rollbackValues: unknown[] = []
            let valueIndex = 3
            
            Object.entries(dbUpdateData).forEach(([key]) => {
              rollbackFields.push(`${key} = $${valueIndex}`)
              rollbackValues.push(originalFaq[key] || null)
              valueIndex++
            })
            
            if (rollbackFields.length > 0) {
              const rollbackSql = `
                UPDATE faqs 
                SET ${rollbackFields.join(', ')}
                WHERE client_id = $1 AND faq_id = $2
              `
              await query(rollbackSql, [clientId, faq_id, ...rollbackValues])
              console.log('Database rollback completed successfully')
            }
          } catch (rollbackError) {
            console.error('Failed to rollback database changes:', rollbackError)
          }
        }
        
        // If we uploaded a new audio file, clean it up since webhook failed
        if (updateData.audio_file_path) {
          try {
            const deleteNewAudioCommand = new DeleteObjectCommand({
              Bucket: BUCKET_NAME,
              Key: updateData.audio_file_path,
            })
            await r2Client.send(deleteNewAudioCommand)
            console.log('Cleaned up new audio file after webhook failure:', updateData.audio_file_path)
          } catch (cleanupError) {
            console.error('Failed to cleanup new audio file after rollback:', cleanupError)
          }
        }
        
        throw new Error(`FAQ processing failed in webhook: ${webhookError instanceof Error ? webhookError.message : 'Unknown error'}`)
      }
    }

    // Clean up old audio file ONLY after webhook success
    if (updateData.answerChanged && originalFaq?.audio_url && originalFaq?.audio_file_path) {
      try {
        const deleteOldAudioCommand = new DeleteObjectCommand({
          Bucket: BUCKET_NAME,
          Key: originalFaq.audio_file_path,
        })
        
        // Background cleanup - don't await to avoid slowing response
        r2Client.send(deleteOldAudioCommand).catch(cleanupError => {
          console.error('Background cleanup failed for old audio file:', originalFaq.audio_file_path, cleanupError)
        })
        
        console.log('Scheduled cleanup of old audio file after successful update:', originalFaq.audio_file_path)
      } catch (cleanupError) {
        console.error('Error scheduling cleanup of old audio file:', cleanupError)
        // Don't fail the operation if cleanup scheduling fails
      }
    }
    
    // Return the new audio URL if audio was processed
    const responseBody = updateData.audio_url ? {
      audio_url: updateData.audio_url,
      audio_file_path: updateData.audio_file_path
    } : null

    return NextResponse.json({
      success: true,
      body: responseBody,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in knowledge lists PUT API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}
