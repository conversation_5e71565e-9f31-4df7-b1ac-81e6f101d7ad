import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { nanoid } from 'nanoid'
import { v4 as uuidv4 } from 'uuid'
import { getM4AExtension } from '@/utils/audioConversion'
import { serverCache } from '@/lib/cache'
import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'
import { transaction, queryOne } from '@/lib/postgres'

// Validate R2 environment variables
const validateR2Config = () => {
  const requiredEnvVars = [
    'CLOUDFLARE_R2_ENDPOINT',
    'CLOUDFLARE_R2_ACCESS_KEY_ID',
    'CLOUDFLARE_R2_SECRET_ACCESS_KEY',
    'CLOUDFLARE_R2_BUCKET_NAME',
    'CLOUDFLARE_R2_PUBLIC_URL'
  ]
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`)
    }
  }
}

// Validate configuration on module load
validateR2Config()

// Initialize R2 client with proper configuration for Cloudflare R2
const r2Client = new S3Client({
  region: 'auto', // R2 uses 'auto' region
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
  forcePathStyle: true, // Required for R2 compatibility
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME!
const PUBLIC_URL = process.env.CLOUDFLARE_R2_PUBLIC_URL!

// Helper function to determine content type from file extension
const getContentType = (fileName: string): string => {
  const ext = fileName.toLowerCase().split('.').pop()
  switch (ext) {
    case 'm4a':
      return 'audio/mp4'
    case 'mp3':
      return 'audio/mpeg'
    case 'mp4':
      return 'audio/mp4'
    case 'webm':
      return 'audio/webm'
    case 'ogg':
      return 'audio/ogg'
    case 'wav':
      return 'audio/wav'
    case 'aac':
      return 'audio/aac'
    default:
      return 'audio/mp4' // Default to mp4 for M4A files
  }
}

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
        return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Parse FormData request
    const formData = await request.formData()
    const faqBatchString = formData.get('faqBatch') as string
    const sectorFromRequest = formData.get('sector') as string | null
    const langFromRequest = formData.get('lang') as string | null
    
    if (!faqBatchString) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required field: faqBatch'
      }, { status: 400 })
    }

    const faqBatch = JSON.parse(faqBatchString)

    // Validate required fields
    if (!faqBatch || !Array.isArray(faqBatch) || faqBatch.length === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required field: faqBatch'
      }, { status: 400 })
    }


    // Validate batch size (max 20 FAQs)
    const MAX_BATCH_SIZE = 10
    if (faqBatch.length > MAX_BATCH_SIZE) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: `Batch size ${faqBatch.length} exceeds limit of ${MAX_BATCH_SIZE}`
      }, { status: 400 })
    }

    // Validate each FAQ in the batch
    for (let i = 0; i < faqBatch.length; i++) {
      const faq = faqBatch[i]
      
      if (!faq.question || typeof faq.question !== 'string' || faq.question.trim() === '') {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `FAQ ${i + 1}: Question is required`
        }, { status: 400 })
      }

      if (!faq.isAudioAnswer && (!faq.answer || typeof faq.answer !== 'string' || faq.answer.trim() === '')) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `FAQ ${i + 1}: Answer is required for text FAQs`
        }, { status: 400 })
      }

      if (faq.isAudioAnswer && (!faq.answer || typeof faq.answer !== 'string' || faq.answer.trim() === '')) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `FAQ ${i + 1}: Audio code is required for audio FAQs`
        }, { status: 400 })
      }
      
      // Validate audio blob exists for audio FAQs
      if (faq.isAudioAnswer && faq.audioIndex !== undefined && faq.audioIndex !== null) {
        const audioBlob = formData.get(`audioBlob_${faq.audioIndex}`)
        if (!audioBlob) {
          return NextResponse.json({
            success: false,
            body: null,
            error_msg: `FAQ ${i + 1}: Missing processed audio blob for audio FAQ`
          }, { status: 400 })
        }
      }
    }

    // Generate batch ID 
    const batchId = nanoid(12)
    
    // Step 1: Split FAQs by type
    const textFaqs = faqBatch.filter(faq => !faq.isAudioAnswer)
    const audioFaqs = faqBatch.filter(faq => faq.isAudioAnswer)
    
    
    // Step 2: Process audio FAQs with direct parallel R2 upload (no chunking)
    const processedAudioFaqs: Array<{
      faq_id: string
      question: string
      answer: string
      audio_url: string | null
      audio_duration: number | null
      audio_file_path: string | null
      photo_id: string | null
      photo_urls: string[] | null
      is_audio: boolean
      success: boolean
      error?: string
    }> = []
    
    // Track successful uploads for cleanup in case of batch failure
    const successfulUploads: string[] = []
    
    if (audioFaqs.length > 0) {
      // Process ALL audio FAQs in parallel - no chunking
      const allAudioResults = await Promise.allSettled(
        audioFaqs.map(async (faq) => {
          try {
            // Get pre-processed audio blob from FormData
            const audioBlob = (faq.audioIndex !== undefined && faq.audioIndex !== null) ? formData.get(`audioBlob_${faq.audioIndex}`) as File : null
            
            if (!audioBlob) {
              throw new Error('Missing processed audio blob for audio FAQ')
            }
            
            // Generate audio ID and file path with timestamp-UUID format (consistent with lists endpoint)
            const timestamp = Date.now()
            const uniqueId = uuidv4()
            const audioId = `${timestamp}-${uniqueId}`
            const fileExtension = getM4AExtension()
            const fileName = `${audioId}.${fileExtension}`
            const filePath = `audios/${authId}/${fileName}`
            
            // Convert blob to buffer for direct R2 upload
            const audioBuffer = Buffer.from(await audioBlob.arrayBuffer())
            const contentType = getContentType(fileName)
            
            // Direct R2 upload - no HTTP overhead
            const putCommand = new PutObjectCommand({
              Bucket: BUCKET_NAME,
              Key: filePath,
              Body: new Uint8Array(audioBuffer),
              ContentType: contentType,
              CacheControl: '3600',
              ContentDisposition: `inline; filename="${fileName}"`,
            })
            
            await r2Client.send(putCommand)
            
            // Track successful upload for potential cleanup
            successfulUploads.push(filePath)
            
            // Generate public URL
            const publicUrl = `${PUBLIC_URL}/${filePath}`
            
            // Return processed FAQ with audio URL
            return {
              faq_id: nanoid(16),
              question: faq.question,
              answer: '', // Empty for audio FAQs
              audio_url: publicUrl,
              audio_duration: faq.audioDuration || null,
              audio_file_path: filePath,
              photo_id: faq.photoInfo?.photo_id || null,
              photo_urls: faq.photoInfo?.full_photo_urls || null,
              is_audio: true,
              success: true
            }
            
          } catch (error) {
            return {
              faq_id: nanoid(16),
              question: faq.question,
              answer: '',
              audio_url: null,
              audio_duration: faq.audioDuration || null,
              audio_file_path: null,
              photo_id: faq.photoInfo?.photo_id || null,
              photo_urls: faq.photoInfo?.full_photo_urls || null,
              is_audio: true,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          }
        })
      )
      
      // Process results from Promise.allSettled
      processedAudioFaqs.push(...allAudioResults.map(result => 
        result.status === 'fulfilled' ? result.value : result.reason
      ))
    }
    
    // Step 3: Prepare text FAQs (no processing needed)
    const processedTextFaqs = textFaqs.map(faq => ({
      faq_id: nanoid(16),
      question: faq.question,
      answer: faq.answer,
      audio_url: null,
      audio_duration: null,
      audio_file_path: null,
      photo_id: faq.photoInfo?.photo_id || null,
      photo_urls: faq.photoInfo?.full_photo_urls || null,
      is_audio: false,
      success: true,
      error: undefined
    }))
    
    // Step 4: Combine all processed FAQs
    const allProcessedFaqs = [...processedTextFaqs, ...processedAudioFaqs]
    const successfulFaqs = allProcessedFaqs.filter(faq => faq.success)
    const failedFaqs = allProcessedFaqs.filter(faq => !faq.success)
    

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Step 5: Use client info from frontend request (with fallbacks)
    let clientInfo = null
    
    if (sectorFromRequest || langFromRequest) {
      // Use sector/lang passed from frontend
      clientInfo = {
        sector: sectorFromRequest,
        lang: langFromRequest
      }
    } else {
      // Fallback 1: Try knowledge cache
      const knowledgeCacheKey = `knowledge_${authId}`
      const knowledgeData = serverCache.get(knowledgeCacheKey)
      
      if (knowledgeData) {
        clientInfo = {
          sector: knowledgeData.sector,
          lang: knowledgeData.clientLang
        }
      } else {
        // Fallback 2: Get client info from database
        try {
          const clientInfoSql = `
            SELECT sector, lang
            FROM clients
            WHERE auth_id = $1
          `
          const clientData = await queryOne(clientInfoSql, [authId])
          if (clientData) {
            clientInfo = {
              sector: clientData.sector,
              lang: clientData.lang
            }
          }
        } catch (error) {
          }
      }
    }

    // Step 6: Insert successful FAQs directly to PostgreSQL
    let insertedFaqs = []
    if (successfulFaqs.length > 0) {
      try {
        // Use transaction to ensure all FAQs are inserted atomically
        insertedFaqs = await transaction(async (client) => {
          const insertedResults: any[] = []

          for (const faq of successfulFaqs) {
            const insertSql = `
              INSERT INTO faqs (
                client_id, faq_id, question_p, answer_p, audio_url,
                audio_duration, audio_file_path, photo_id, photo_url, is_visible
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
              RETURNING id, faq_id, created_at
            `

            const result = await client.query(insertSql, [
              clientId,
              faq.faq_id,
              faq.question,
              faq.answer,
              faq.audio_url,
              faq.audio_duration,
              faq.audio_file_path,
              faq.photo_id,
              faq.photo_urls, // This will be stored as array in photo_url column
              true // is_visible = true for new FAQs
            ])

            insertedResults.push(result.rows[0])
          }

          return insertedResults
        })


      } catch (error) {
        // Cleanup all uploaded audio files if database insert fails
        if (successfulUploads.length > 0) {
          // Delete all uploaded audio files in parallel (background cleanup)
          Promise.allSettled(
            successfulUploads.map(async (filePath) => {
              try {
                const deleteCommand = new DeleteObjectCommand({
                  Bucket: BUCKET_NAME,
                  Key: filePath,
                })
                await r2Client.send(deleteCommand)
              } catch (cleanupError) {
                console.error('Failed to cleanup audio file after database failure:', filePath, cleanupError)
              }
            })
          ).catch(() => {
            // Ignore cleanup promise errors - this is background cleanup
          })
        }
        
        throw new Error(`Failed to save FAQs to database: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }

      // Step 6b: Send each FAQ to webhook in parallel and wait for all to complete
      if (webhookUrl) {
        
        // Helper function to convert blob to base64 (Node.js compatible)
        const blobToBase64 = async (blob: Blob): Promise<string> => {
          const arrayBuffer = await blob.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);
          return buffer.toString('base64');
        }

        const webhookPromises = successfulFaqs.map(async (faq, index) => {
          try {
            
            // Get audio blob for base64 conversion if this is an audio FAQ
            let audioBase64 = null;
            let audioFilename = null;
            
            if (faq.is_audio && faq.audio_file_path) {
              // Find the corresponding audio FAQ from original batch to get the blob
              const originalAudioFaq = audioFaqs.find(originalFaq => 
                originalFaq.question === faq.question && originalFaq.answer !== faq.answer
              );
              
              if (originalAudioFaq && originalAudioFaq.audioIndex !== undefined && originalAudioFaq.audioIndex !== null) {
                const audioBlob = formData.get(`audioBlob_${originalAudioFaq.audioIndex}`) as File;
                if (audioBlob) {
                  audioBase64 = await blobToBase64(audioBlob);
                  audioFilename = audioBlob.name;
                }
              }
            }

            const payload = {
              mode: 'faq',
              operation: 'add_single',
              conversation_id: uuidv4(),
              faq_id: faq.faq_id,
              client_id: clientId,
              question: faq.question,
              answer: faq.answer,
              sector: clientInfo?.sector || null,
              lang: clientInfo?.lang || null,
              audio_url: audioBase64 || faq.audio_url, // Use base64 in audio_url field if available
              photo_id: faq.photo_id,
              photo_urls: faq.photo_urls
            }


            const response = await fetch(webhookUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${jwtToken}`
              },
              body: JSON.stringify(payload)
            });


            if (!response.ok) {
              const errorText = await response.text()
                throw new Error(`Webhook failed for FAQ ${faq.faq_id}: ${response.status} - ${errorText}`);
            }

            // Handle response body from webhook
            const responseText = await response.text();
            
            let result = null;
            if (responseText.trim()) {
              try {
                result = JSON.parse(responseText);
                
                // Check success field - handle both direct response and array response from N8N
                const responseData = Array.isArray(result) ? result[0] : result;
                
                if (responseData && responseData.success === false) {
                  throw new Error(`Webhook processing failed: ${responseData.error_msg || 'Unknown error'}`);
                }
                
              } catch (jsonError) {
                if (jsonError instanceof Error && jsonError.message && jsonError.message.startsWith('Webhook processing failed:')) {
                  throw jsonError; // Re-throw webhook failures
                }
                result = { success: true, message: responseText };
              }
            } else {
              result = { success: true, message: 'Empty response' };
            }
            
            return result;
          } catch (error) {
            throw error
          }
        });

        // Wait for all webhook calls to complete
        const webhookResults = await Promise.allSettled(webhookPromises);
        const successful = webhookResults.filter(r => r.status === 'fulfilled');
        const failed = webhookResults.filter(r => r.status === 'rejected');


        if (failed.length > 0) {
          
          // Get the FAQ IDs that failed webhook processing
          const failedFaqIds = failed.map((result, index) => {
            const failedIndex = webhookResults.findIndex(r => r === result)
            return successfulFaqs[failedIndex]?.faq_id
          }).filter(Boolean)
          
          
          // Rollback: Delete only the failed FAQs from database
          if (failedFaqIds.length > 0) {
            try {
              const deleteSql = `DELETE FROM faqs WHERE client_id = $1 AND faq_id = ANY($2)`
              await queryOne(deleteSql, [clientId, failedFaqIds])
            } catch (rollbackError) {
            }
          }

          // Cleanup R2 audio files for failed webhook FAQs
          const failedAudioFiles = successfulFaqs
            .filter(faq => failedFaqIds.includes(faq.faq_id) && faq.is_audio && faq.audio_file_path)
            .map(faq => faq.audio_file_path!)

          if (failedAudioFiles.length > 0) {
            // Delete failed audio files in parallel (background cleanup)
            Promise.allSettled(
              failedAudioFiles.map(async (filePath) => {
                try {
                  const deleteCommand = new DeleteObjectCommand({
                    Bucket: BUCKET_NAME,
                    Key: filePath,
                  })
                  await r2Client.send(deleteCommand)
                } catch (cleanupError) {
                  console.error('Failed to cleanup audio file after webhook failure:', filePath, cleanupError)
                }
              })
            ).catch(() => {
              // Ignore cleanup promise errors - this is background cleanup
            })
          }
          
          // Return partial success response with details
          return NextResponse.json({
            success: false,
            partial_success: true,
            body: {
              items_processed: successful.length,
              items_failed: failed.length,
              total_items: successfulFaqs.length,
              batch_id: batchId,
              successful_faq_ids: successful.map((result, index) => {
                const successIndex = webhookResults.findIndex(r => r === result)
                return successfulFaqs[successIndex]?.faq_id
              }).filter(Boolean),
              failed_faq_ids: failedFaqIds,
              inserted_faqs: insertedFaqs?.filter(faq => !failedFaqIds.includes(faq.faq_id)) || null
            },
            error_msg: `${failed.length}/${successfulFaqs.length} FAQ processing failed in webhook. ${successful.length} FAQs were successfully saved.`
          })
        }

      }
    }

    // Step 7: Update knowledge cache with new FAQ count
    try {
      const knowledgeCacheKey = `knowledge_${authId}`
      const knowledgeData = serverCache.get(knowledgeCacheKey)
      
      if (knowledgeData) {
        // Update FAQ count in cache
        const currentFaqCount = knowledgeData.knowledgeStats.faqCount || 0
        const newFaqCount = currentFaqCount + successfulFaqs.length
        const faqLimit = knowledgeData.knowledgeStats.faqLimit || 1

        knowledgeData.knowledgeStats.faqCount = newFaqCount
        knowledgeData.knowledgeStats.faqUsagePercentage = Math.round((newFaqCount / faqLimit) * 100)

        serverCache.set(knowledgeCacheKey, knowledgeData, 30)
      } else {
        // If cache was empty, we can't update it
      }
    } catch (error) {
    }

    // Step 8: Return combined results
    return NextResponse.json({
      success: true,
      body: {
        items_processed: successfulFaqs.length,
        items_failed: failedFaqs.length,
        total_items: allProcessedFaqs.length,
        batch_id: batchId,
        failures: failedFaqs.map(faq => ({
          question: faq.question,
          error: faq.error
        })),
        inserted_faqs: insertedFaqs || null
      },
      error_msg: failedFaqs.length > 0 ? `${failedFaqs.length} items failed to process` : null
    })

  } catch (error: unknown) {
    // Cleanup any uploaded audio files on critical errors
    if (typeof successfulUploads !== 'undefined' && successfulUploads.length > 0) {
      // Delete all uploaded audio files in parallel (background cleanup)
      Promise.allSettled(
        successfulUploads.map(async (filePath) => {
          try {
            const deleteCommand = new DeleteObjectCommand({
              Bucket: BUCKET_NAME,
              Key: filePath,
            })
            await r2Client.send(deleteCommand)
          } catch (cleanupError) {
            console.error('Failed to cleanup audio file after critical error:', filePath, cleanupError)
          }
        })
      ).catch(() => {
        // Ignore cleanup promise errors - this is background cleanup
      })
    }

    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}