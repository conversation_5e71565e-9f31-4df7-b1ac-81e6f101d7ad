import { NextResponse } from 'next/server'
import { serverCache } from '@/lib/cache'
import { verifyAuth } from '@/utils/auth'

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, authId } = await verifyAuth()
    
    if (!authenticated || !authId) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Clear only this user's dashboard cache
    const dashboardCacheKey = `dashboard_${authId}`
    serverCache.delete(dashboardCacheKey)
    
    return NextResponse.json({
      success: true,
      message: 'User dashboard cache cleared successfully'
    })
  } catch (error) {
    console.error('Error clearing server cache:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to clear server cache'
      },
      { status: 500 }
    )
  }
}