import { NextResponse } from 'next/server';
import { registrationRedis } from '@/utils/redis';

export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json();
    const { code, plan, username, sector, lang, timeZone, ttl } = body;

    // Validate required parameters
    if (!code || !plan || !username || !sector || !lang || !timeZone || !ttl) {
      return NextResponse.json(
        { error: 'Missing required parameters: code, plan, username, sector, lang, timeZone, ttl' },
        { status: 400 }
      );
    }

    // Store registration data in Redis
    const registrationData = {
      plan,
      username,
      sector,
      lang,
      timeZone
    };

    const result = await registrationRedis.setRegistrationData(code, registrationData, ttl);

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to store registration data' },
        { status: 500 }
      );
    }

    return NextResponse.json({ 
      success: true,
      message: 'Registration data stored successfully'
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to store registration data' },
      { status: 500 }
    );
  }
}
