import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { queryOne } from '@/lib/postgres'

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized' 
      }, { status: 401 })
    }

    // Validate client ID
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Parse request body
    const { platform } = await request.json()

    if (!platform) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required field: platform'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // For Telegram Bot, clear webhook BEFORE clearing database token
    if (platform === 'telegram') {
      try {
        // Get current token before we clear it
        const credentials = await queryOne(
          `SELECT tg_token, tg_biz_id FROM client_credentials WHERE client_id = $1`,
          [clientId]
        );

        // Only clear webhook if it's a bot connection (has tg_token, not tg_biz_id for business)
        if (credentials?.tg_token && !credentials?.tg_biz_id) {
          console.log('Clearing Telegram webhook for bot token...');
          
          // Call Telegram API to clear webhook
          const telegramResponse = await fetch(`https://api.telegram.org/bot${credentials.tg_token}/setWebhook`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              url: '',  // Clear webhook
              drop_pending_updates: true
            }),
          });

          if (!telegramResponse.ok) {
            console.warn('Failed to clear Telegram webhook, but continuing with disconnect...');
          } else {
            console.log('Successfully cleared Telegram webhook');
          }
        } else if (credentials?.tg_biz_id) {
          console.log('Telegram Business connection - webhook clearing not needed');
        }
      } catch (error) {
        console.warn('Error clearing Telegram webhook, but continuing with disconnect:', error);
        // Continue with disconnect even if webhook clearing fails
      }
    }

    // Build SQL queries for platform disconnect
    const sql = `SELECT fb_url, ig_url, tg_url, web_url, tg_biz_id FROM client_credentials WHERE client_id = $1`
    let sqlUpdate = ''
    const params = [clientId, 0]
    
    switch (platform) {
      case 'facebook':
        sqlUpdate = `UPDATE client_credentials SET fb_name = NULL, fb_id = NULL, fb_token = NULL, fb_status = $2 WHERE client_id = $1`
        break
      case 'instagram':
        sqlUpdate = `UPDATE client_credentials SET ig_name = NULL, ig_id = NULL, ig_token = NULL, ig_status = $2 WHERE client_id = $1`
        break
      case 'telegram':
        sqlUpdate = `UPDATE client_credentials SET tg_name = NULL, tg_id = NULL, tg_id_name = NULL, tg_token = NULL, tg_biz_id = NULL, tg_status = $2 WHERE client_id = $1`
        break
      case 'web':
        sqlUpdate = `UPDATE client_credentials SET web_domain = NULL, web_name = NULL, web_status = $2 WHERE client_id = $1`
        break
      default:
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `Unknown platform: ${platform}`
        }, { status: 400 })
    }

    // Send single webhook call with SQL in body
    const webhookResponse = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'disconnect',
        mode: 'connection',
        client_id: clientId,
        platform: platform,
        sql: sql,
        sql_update: sqlUpdate,
        params: params
      })
    })

    if (!webhookResponse.ok) {
      throw new Error(`Disconnect webhook request failed: ${webhookResponse.status} ${webhookResponse.statusText}`)
    }

    let webhookData = await webhookResponse.json()
    
    // Handle N8N array wrapper
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      return NextResponse.json({
        success: false,
        body: webhookData.body || null,
        error_msg: webhookData.error_msg || 'Disconnect operation failed'
      }, { status: 400 })
    }


    return NextResponse.json({
      success: true,
      body: {
        message: `Successfully disconnected ${platform}`,
        platform
      },
      error_msg: null
    })

  } catch (error) {
    console.error('Error in disconnect API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}