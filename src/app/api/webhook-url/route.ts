import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { queryOne } from '@/lib/postgres'

export async function POST(request: Request) {
  try {
    const { webhookId } = await request.json()

    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized' 
      }, { status: 401 })
    }

    // Validate client ID
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }


    // Get client credentials directly from PostgreSQL
    const credentials = await queryOne(`
      SELECT fb_url, ig_url, wa_url, tg_url, web_url 
      FROM client_credentials 
      WHERE client_id = $1
    `, [clientId])

    // Handle missing credentials for webhook URLs (not for privacy policy)
    if (!credentials && webhookId !== 'privacy-policy') {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No connection credentials found. Please contact support.'
      }, { status: 404 })
    }

    // Return the appropriate webhook URL
    let platformWebhookUrl = '';

    if (webhookId === 'privacy-policy') {
      platformWebhookUrl = 'https://www.chhlatbot.com/privacy';
    } else if (webhookId === 'fb-webhook') {
      platformWebhookUrl = credentials?.fb_url || 'https://api.chhlatbot.com/webhook/messenger';
    } else if (webhookId === 'ig-webhook') {
      platformWebhookUrl = credentials?.ig_url || 'https://api.chhlatbot.com/webhook/instagram';
    } else if (webhookId === 'wa-webhook') {
      platformWebhookUrl = credentials?.wa_url || 'https://api.chhlatbot.com/webhook/whatsapp';
    } else if (webhookId === 'web-webhook') {
      platformWebhookUrl = credentials?.web_url || 'https://api.chhlatbot.com/webhook/web';
    } else {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Invalid webhook ID'
      }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      body: { url: platformWebhookUrl },
      error_msg: null
    })

  } catch (error) {
    console.error('Error in webhook-url API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}
