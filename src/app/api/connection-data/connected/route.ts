import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { queryOne } from '@/lib/postgres'

// Define connected platform type
type ConnectedPlatform = {
  platform: string
  displayName: string
  status: number
  connectionType?: string // For telegram bot/business distinction
}

export async function GET() {
  try {
    // Authentication check
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized' 
      }, { status: 401 })
    }

    // Validate client ID
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Query for connected platforms only
    const result = await queryOne(`
      SELECT fb_name, ig_name, tg_name, web_name, web_domain, 
             fb_status, ig_status, tg_status, web_status, tg_biz_id
      FROM client_credentials 
      WHERE client_id = $1
    `, [clientId])

    // If no credentials record exists, return empty array
    if (!result) {
      return NextResponse.json({
        success: true,
        body: { connectedPlatforms: [] },
        error_msg: null
      })
    }

    const connectedPlatforms: ConnectedPlatform[] = []

    // Simple filter: only add platforms that have names
    if (result.fb_name) {
      connectedPlatforms.push({
        platform: 'facebook',
        displayName: result.fb_name,
        status: result.fb_status || 1
      })
    }

    if (result.ig_name) {
      connectedPlatforms.push({
        platform: 'instagram', 
        displayName: result.ig_name,
        status: result.ig_status || 1
      })
    }

    if (result.tg_name) {
      connectedPlatforms.push({
        platform: 'telegram',
        displayName: result.tg_name,
        connectionType: result.tg_biz_id ? 'business' : 'bot',
        status: result.tg_status || 1
      })
    }

    if (result.web_name || result.web_domain) {
      connectedPlatforms.push({
        platform: 'web',
        displayName: result.web_domain || result.web_name,
        status: result.web_status || 1
      })
    }

    return NextResponse.json({
      success: true,
      body: { connectedPlatforms },
      error_msg: null
    })

  } catch (error) {
    console.error('Error in connected platforms API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}