import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { serverCache } from '@/lib/cache'
import { queryOne } from '@/lib/postgres'

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Only dashboard cache is supported now
    const cacheKey = `dashboard_${authId}`
    const cachedResult = serverCache.get(cacheKey)
    if (cachedResult) {
      return NextResponse.json({
        success: true,
        body: cachedResult,
        error_msg: null,
        cached: true
      })
    }

    // Consolidated dashboard cache SQL - include all necessary data
    const sql = `
      SELECT
        c.client_id,
        c.username,
        c.sector,
        c.lang,
        c.plan_type,
        c.next_billing_date,
        c.usage_used,
        c.usage_limit,
        c.cmt_used,
        c.cmt_limit,
        c.status,
        COALESCE(p.total_faqs, 0) as faq_limit,
        (SELECT COUNT(*) FROM faqs WHERE client_id = c.client_id AND is_visible = true) as faq_count
      FROM clients c
      LEFT JOIN plans p ON p.name = c.plan_type
      WHERE c.auth_id = $1
    `

    // Execute direct PostgreSQL query
    const row = await queryOne(sql, [authId])

    if (!row || !row.client_id) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No dashboard data found for user'
      }, { status: 404 })
    }


    // Format consolidated dashboard response
    const faqUsagePercentage = row.faq_limit > 0 ? Math.min((row.faq_count / row.faq_limit) * 100, 100) : 0
    const commentUsagePercentage = row.cmt_limit > 0 ? Math.min((row.cmt_used / row.cmt_limit) * 100, 100) : 0

    const responseData = {
      clientInfo: {
        username: row.username,
        sector: row.sector,
        lang: row.lang,
        plan_type: row.plan_type,
        next_billing_date: row.next_billing_date,
        status: row.status
      },
      usageData: {
        usage_used: row.usage_used || 0,
        usage_limit: row.usage_limit || 100,
        cmt_used: row.cmt_used || 0,
        cmt_limit: row.cmt_limit || 2000
      },
      knowledgeStats: {
        faqCount: parseInt(row.faq_count) || 0,
        faqLimit: parseInt(row.faq_limit) || 0,
        faqUsagePercentage: Math.round(faqUsagePercentage)
      },
      commentStats: {
        commentUsed: row.cmt_used || 0,
        commentLimit: row.cmt_limit || 2000,
        commentUsagePercentage: Math.round(commentUsagePercentage)
      }
    }

    // Cache the result for 30 minutes
    serverCache.set(cacheKey, responseData, 30)

    return NextResponse.json({
      success: true,
      body: responseData,
      error_msg: null
    })
  } catch (error) {
    console.error('Error in user data API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}