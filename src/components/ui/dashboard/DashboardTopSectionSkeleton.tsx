"use client";

import React from "react";
import { useTheme, useThemeConfig } from "@/context/ThemeContext";

export default function DashboardTopSectionSkeleton() {
  const { theme } = useTheme();
  const themeConfig = useThemeConfig();

  const cardStyle =
    theme === "dark"
      ? {
          boxShadow:
            "0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)",
        }
      : {
          boxShadow:
            "0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)",
        };

  const base = `${themeConfig.card} border ${themeConfig.border}`;
  const text = `${themeConfig.text}`;

  return (
    <>
      {themeConfig.backgroundEffects}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${base} rounded-2xl px-4 py-3 transition-all duration-300 overflow-hidden`}
            style={cardStyle}
          >
            {/* Top row: logo + user/email placeholder */}
            <div className="relative z-10 flex items-center justify-between">
              <div className={`h-8 w-32 rounded-md ${themeConfig.skeletonElement}`}></div>
              <div className="flex items-center space-x-3">
                <div className={`h-6 w-24 rounded-md ${themeConfig.skeletonElement}`}></div>
                <div className={`h-8 w-8 rounded-full ${themeConfig.skeletonElement}`}></div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-3">
        {/* Plan + stats row */}
        <div
          className={`mt-2 ${base} rounded-2xl px-4 py-3 transition-all duration-300 overflow-hidden`}
          style={cardStyle}
        >
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
            <div className="flex items-center space-x-3">
              <div className={`h-5 w-28 rounded ${themeConfig.skeletonElement}`}></div>
              <div className={`h-4 w-40 rounded ${themeConfig.skeletonElement}`}></div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 w-full md:w-auto">
              <div className={`h-4 w-full rounded ${themeConfig.skeletonElement}`}></div>
              <div className={`h-4 w-full rounded ${themeConfig.skeletonElement}`}></div>
              <div className={`h-4 w-full rounded ${themeConfig.skeletonElement}`}></div>
            </div>
          </div>
        </div>

        {/* Nav buttons row */}
        <div className="mt-2 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2">
          {Array.from({ length: 6 }).map((_, i) => (
            <div
              key={i}
              className={`h-9 rounded-xl ${themeConfig.skeletonElement} border ${themeConfig.border}`}
            />
          ))}
        </div>
      </div>
    </>
  );
}
