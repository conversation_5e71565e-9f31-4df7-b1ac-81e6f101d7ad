'use client'

import { useRef, useCallback, useState, useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { optimizeGalleryImage } from '@/utils/imageOptimization'

// Smart Image component that adjusts sizing based on aspect ratio and screen size
const SmartImage = ({ src, alt }: { src: string; alt: string }) => {
  const [dimensions, setDimensions] = useState<{ width: number; height: number } | null>(null)
  const [imageLoaded, setImageLoaded] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  
  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768) // md breakpoint
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])
  
  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const img = e.currentTarget
    const aspectRatio = img.naturalWidth / img.naturalHeight
    setDimensions({ width: img.naturalWidth, height: img.naturalHeight })
    setImageLoaded(true)
    
    // Log for debugging
    console.log(`Image loaded: ${img.naturalWidth}x${img.naturalHeight}, aspect ratio: ${aspectRatio.toFixed(2)}, mobile: ${isMobile}`)
  }

  // Calculate smart sizing classes based on aspect ratio and device
  const getImageClasses = () => {
    if (!dimensions) return 'max-w-full max-h-full object-contain' // Default fallback
    
    const aspectRatio = dimensions.width / dimensions.height
    
    // Mobile: Always use contain to show full image
    if (isMobile) {
      return 'max-w-full max-h-full object-contain'
    }
    
    // Desktop: Smart sizing based on aspect ratio
    // Landscape photos (like 4x6 = 1.5, or wider)
    if (aspectRatio >= 1.3) {
      return 'w-full h-auto max-h-full object-contain' // Fill width for landscape
    }
    // Portrait photos (like 6x4 = 0.67, 2x3 = 0.67, 4x6 portrait = 0.67)
    // For desktop, we want to show the full image, not fill height
    else if (aspectRatio <= 0.9) {
      return 'max-w-full max-h-full object-contain' // Show full portrait image
    }
    // Square or nearly square photos
    else {
      return 'max-w-full max-h-full object-contain' // Standard contain behavior
    }
  }

  return (
    <div className="flex items-center justify-center w-full h-full">
      {!imageLoaded && (
        <div className="animate-pulse bg-gray-300 dark:bg-gray-700 rounded-lg w-full h-full flex items-center justify-center">
          <svg className="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
        </div>
      )}
      <img
        src={src}
        alt={alt}
        className={`rounded-lg transition-opacity duration-300 ${getImageClasses()} ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
        loading="eager"
        decoding="async"
        onLoad={handleImageLoad}
        onError={(e) => {
          (e.target as HTMLImageElement).src = '/placeholder-image.svg';
          setImageLoaded(true)
        }}
      />
    </div>
  )
}

// PhotoThumbnail component - copied from main file to avoid circular imports
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  return (
    <div
      className={`${className} ${themeConfig.card} rounded overflow-hidden flex-shrink-0 relative border ${themeConfig.border} ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'} ${themeConfig.skeletonElement}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className={`w-full h-full flex items-center justify-center ${themeConfig.skeletonElement} ${themeConfig.textMuted} text-xs`}>
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

interface ImageGalleryModalProps {
  imageGallery: {
    urls: string[]
    currentIndex: number
  } | null
  onClose: () => void
  onImageChange: (index: number) => void
  onPrevious: () => void
  onNext: () => void
}

export default function ImageGalleryModal({ 
  imageGallery, 
  onClose, 
  onImageChange, 
  onPrevious, 
  onNext 
}: ImageGalleryModalProps) {
  const { t } = useLanguage()
  const themeConfig = useThemeConfig()
  const imageGalleryRef = useRef<HTMLDivElement>(null)


  // Touch support for image gallery
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && imageGallery) {
      onNext();
    }
    if (isRightSwipe && imageGallery) {
      onPrevious();
    }
  };

  if (!imageGallery) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-20 backdrop-blur-sm flex items-center justify-center z-50">
      <div
        ref={imageGalleryRef}
        className={`relative ${themeConfig.card} rounded-2xl p-6 w-full max-w-3xl mx-4 border ${themeConfig.border} overflow-hidden`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        <div className="relative z-10">
          {/* Close button in the top-right corner */}
          <button
            onClick={onClose}
            className={`absolute top-0 right-0 p-1.5 border ${themeConfig.border} rounded-full ${themeConfig.interactive} hover:bg-jade-purple ${themeConfig.textSecondary} hover:text-white transition-colors z-20`}
            aria-label="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          <div className="flex flex-col">
            {/* Image counter */}
            <div className={`text-center mb-2 text-sm font-body ${themeConfig.textMuted}`}>
              {imageGallery.currentIndex + 1} / {imageGallery.urls.length}
            </div>

            {/* Main image container with responsive height */}
            <div
              className="relative h-[50vh] md:h-[75vh] flex items-center justify-center"
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
              aria-live="polite"
              role="region"
              aria-label={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
            >
              {imageGallery.urls.length === 0 ? (
                <div className={`text-center ${themeConfig.textMuted} p-8 ${themeConfig.interactive} rounded-lg`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className={`h-16 w-16 mx-auto mb-4 ${themeConfig.textMuted}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p className="font-body">{t('no_images_available')}</p>
                </div>
              ) : (
                <div className="relative">
                  <SmartImage
                    src={optimizeGalleryImage(imageGallery.urls[imageGallery.currentIndex])}
                    alt={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                  />
                </div>
              )}

              {/* Navigation buttons - only show if more than one image */}
              {imageGallery.urls.length > 1 && (
                <>
                  {/* Previous button */}
                  <button
                    onClick={onPrevious}
                    className="absolute left-2 p-2 rounded-full bg-black/20 hover:bg-black/60 text-white transition-colors"
                    aria-label="Previous image"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>

                  {/* Next button */}
                  <button
                    onClick={onNext}
                    className="absolute right-2 p-2 rounded-full bg-black/20 hover:bg-black/60 text-white transition-colors"
                    aria-label="Next image"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </>
              )}
            </div>

            {/* Thumbnail strip - only show if more than one image */}
            {imageGallery.urls.length > 1 && (
              <div className="flex justify-center space-x-2 mt-4 overflow-x-auto py-2">
                {imageGallery.urls.map((url, index) => (
                  <PhotoThumbnail
                    key={index}
                    photo={{
                      photo_url: [url],
                      photo_id: `thumbnail-${index + 1}`
                    }}
                    className={`w-16 h-16 rounded-lg border-2 transition-all duration-200 ease-in-out ${
                      index === imageGallery.currentIndex ? `${themeConfig.borderActive} scale-105 opacity-100` : `${themeConfig.border} opacity-70 hover:opacity-90 ${themeConfig.borderHover}`
                    }`}
                    onClick={() => onImageChange(index)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}