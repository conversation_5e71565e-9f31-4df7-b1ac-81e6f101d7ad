'use client'

import { useRef, useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button } from '@/components/ui'

interface CancelConfirmationModalProps {
  showCancelConfirmation: boolean
  onKeepEditing: () => void
  onConfirmDiscard: () => void
  isLoading?: boolean
}

export default function CancelConfirmationModal({ 
  showCancelConfirmation, 
  onKeepEditing, 
  onConfirmDiscard,
  isLoading = false
}: CancelConfirmationModalProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const cancelConfirmModalRef = useRef<HTMLDivElement>(null)


  // Handle escape key to keep editing (cancel action)
  useEffect(() => {
    if (showCancelConfirmation) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          event.preventDefault()
          onKeepEditing()
        }
      }
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [showCancelConfirmation, onKeepEditing])

  if (!showCancelConfirmation) return null

  return (
    <div
      className={`fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50`}
    >
      <div
        ref={cancelConfirmModalRef}
        className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-900/50'}`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        {theme === 'dark' && (
          <div className="absolute inset-0 bg-gradient-to-br from-red-900/30 to-transparent opacity-50 rounded-2xl"></div>
        )}
        <div className="relative z-10">
          <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.text}`}>
            {t('toggle_platform_confirmation')}
          </h3>
          <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
            {t('toggle_platform_warning')}
          </p>
          <div className="flex justify-between w-full space-x-4">
            <Button
              onClick={onKeepEditing}
              variant="cancel"
              size="md"
              className="flex-1"
              disabled={isLoading}
            >
              {t('cancel')}
            </Button>
            <Button
              onClick={onConfirmDiscard}
              variant="danger"
              size="md"
              className="flex-1"
              isLoading={isLoading}
              disabled={isLoading}
            >
              {t('confirm')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}