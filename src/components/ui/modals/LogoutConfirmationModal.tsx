'use client'

import { useRef, useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button } from '@/components/ui'

interface LogoutConfirmationModalProps {
  showConfirmation: boolean
  isLoading: boolean
  onCancel: () => void
  onConfirmLogout: () => void
}

export default function LogoutConfirmationModal({ 
  showConfirmation, 
  isLoading, 
  onCancel, 
  onConfirmLogout 
}: LogoutConfirmationModalProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const modalRef = useRef<HTMLDivElement>(null)


  // Handle escape key to cancel
  useEffect(() => {
    if (showConfirmation) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          event.preventDefault()
          onCancel()
        }
      }
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [showConfirmation, onCancel])

  if (!showConfirmation) return null

  return (
    <div
      className={`fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50`}
    >
      <div
        ref={modalRef}
        className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        {theme === 'dark' && (
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
        )}
        <div className="relative z-10">
          <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.text}`}>
            {t('settings_confirm_logout')}
          </h3>
          <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
            {t('settings_logout_message')}
          </p>
          <div className="flex justify-between w-full space-x-4">
            <Button
              onClick={onCancel}
              variant="cancel"
              size="md"
              className="flex-1"
              disabled={isLoading}
            >
              {t('cancel')}
            </Button>
            <Button
              onClick={onConfirmLogout}
              variant="danger"
              size="md"
              className="flex-1"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                t('settings_yes_logout')
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}