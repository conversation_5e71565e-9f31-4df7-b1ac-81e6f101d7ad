import { ButtonHTMLAttributes, forwardRef } from 'react';
import { twMerge } from 'tailwind-merge';
import { useTheme, useThemeConfig } from '@/context/ThemeContext';

type ButtonVariant = 'primary' | 'secondary' | 'ghost' | 'outline' | 'danger' | 'success' | 'cancel';
type ButtonSize = 'sm' | 'md' | 'lg' | 'icon';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isLoading?: boolean;
  loadingText?: string;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  isLoading = false,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  loadingText,
  ...props
}, ref) => {
  const { theme } = useTheme();
  const themeConfig = useThemeConfig();
  
  const baseStyles = 'inline-flex items-center justify-center rounded-lg font-body font-medium focus:outline-none focus:ring-0 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200';
  
  const variants = {
    primary: 'bg-jade-purple text-white border border-white/30 hover:bg-jade-purple-dark',
    secondary: `${themeConfig.secondCard} ${themeConfig.text} border ${themeConfig.border} ${themeConfig.secondCardHover}`,
    ghost: `bg-transparent ${themeConfig.text} hover:${themeConfig.interactive} border border-transparent`,
    outline: `bg-transparent ${themeConfig.text} border border-jade-purple/60 hover:border-jade-purple/80 hover:bg-jade-purple/10`,
    danger: 'bg-red-700/90 text-white border border-white/30 hover:bg-red-800/90',
    success: 'bg-green-800/80 text-white border border-green-600/50 hover:bg-green-700/90',
    cancel: `${theme === 'dark' ? 'bg-white/5 text-white border border-white/30 hover:bg-white/10' : 'bg-gray-50 text-gray-700 border border-gray-200 hover:border-gray-300'}`,
  };

  const sizes = {
    sm: 'text-xs px-3 py-1.5',
    md: 'text-sm px-4 py-2',
    lg: 'text-base px-6 py-3',
    icon: 'p-2', 
  };

  return (
    <button
      ref={ref}
      className={twMerge(
        baseStyles,
        variants[variant],
        sizes[size],
        fullWidth && 'w-full',
        className
      )}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <>
          <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
          {loadingText && <span className="ml-2">{loadingText}</span>}
        </>
      ) : (
        <>
          {leftIcon && <span className="mr-2">{leftIcon}</span>}
          {children}
          {rightIcon && <span className="ml-2">{rightIcon}</span>}
        </>
      )}
    </button>
  );
});

Button.displayName = 'Button';

export { Button };
export type { ButtonVariant, ButtonSize };
