'use client'

import { useRef, useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { FaTrash } from 'react-icons/fa'
import { Button } from '@/components/ui'

interface ImageUploadAreaProps {
  isProcessingImage: boolean
  imagePreviews: Array<{id: string, url: string, isExisting?: boolean, url_index?: number}>
  selectedFiles: File[]
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  onRemoveFile: (index: number) => void
  onAddMoreClick: () => void
  uploadError?: string
  maxFiles?: number
  fileInputId: string
  emptyStateText?: string
  className?: string
  onFileDrop?: (files: File[]) => void
}

export default function ImageUploadArea({
  isProcessingImage,
  imagePreviews,
  selectedFiles,
  onFileChange,
  onRemoveFile,
  onAddMoreClick,
  uploadError,
  maxFiles = 4,
  fileInputId,
  emptyStateText,
  className = "",
  onFileDrop
}: ImageUploadAreaProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const dropAreaRef = useRef<HTMLDivElement>(null)

  const triggerFileSelect = () => {
    // Only trigger file selection if there are no images or we're processing
    if (imagePreviews.length === 0 && !isProcessingImage) {
      document.getElementById(fileInputId)?.click()
    }
  }

  // Handle drag and drop functionality
  useEffect(() => {
    const dropArea = dropAreaRef.current;
    if (!dropArea || !onFileDrop) return;

    const highlight = () => dropArea.classList.add('border-jade-purple', 'bg-jade-purple/5');
    const unhighlight = () => {
      dropArea.classList.remove('border-jade-purple', 'bg-jade-purple/5');
    };

    const preventDefaults = (e: Event) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDragEnter = (e: DragEvent) => {
      preventDefaults(e);
      highlight();
    };

    const handleDragOver = (e: DragEvent) => {
      preventDefaults(e);
      highlight();
    };

    const handleDragLeave = (e: DragEvent) => {
      preventDefaults(e);
      // Only unhighlight if we're leaving the drop area entirely
      const rect = dropArea.getBoundingClientRect();
      const x = e.clientX;
      const y = e.clientY;
      
      if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
        unhighlight();
      }
    };

    const handleDrop = (e: DragEvent) => {
      preventDefaults(e);
      unhighlight();

      if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
        const files = Array.from(e.dataTransfer.files);
        // Filter for image files only
        const imageFiles = files.filter(file => 
          file.type.startsWith('image/') && 
          ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)
        );
        
        if (imageFiles.length > 0) {
          onFileDrop(imageFiles);
        }
      }
    };

    // Add event listeners
    dropArea.addEventListener('dragenter', handleDragEnter);
    dropArea.addEventListener('dragover', handleDragOver);
    dropArea.addEventListener('dragleave', handleDragLeave);
    dropArea.addEventListener('drop', handleDrop);

    return () => {
      // Clean up event listeners
      if (dropArea) {
        dropArea.removeEventListener('dragenter', handleDragEnter);
        dropArea.removeEventListener('dragover', handleDragOver);
        dropArea.removeEventListener('dragleave', handleDragLeave);
        dropArea.removeEventListener('drop', handleDrop);
      }
    };
  }, [onFileDrop]);

  return (
    <div>
      <label className={`block text-xs ${themeConfig.textMuted} mb-1`}>
        {t('max_files')}
      </label>
      <div
        ref={dropAreaRef}
        className={`${themeConfig.secondCard} border border-dashed ${themeConfig.border} rounded-lg p-4 flex flex-col items-center justify-center ${imagePreviews.length === 0 ? 'hover:border-jade-purple/75 transition-colors cursor-pointer' : ''} relative ${className}`}
        onClick={triggerFileSelect}
      >
        {isProcessingImage ? (
          <div className="flex flex-col items-center justify-center py-4 md:py-2">
            <div className="w-10 h-10 border-3 border-jade-purple border-t-transparent rounded-full animate-spin mb-2"></div>
            <p className={`${themeConfig.textMuted} text-[10px] sm:text-xs`}>{t('processing_image')}</p>
          </div>
        ) : imagePreviews.length > 0 ? (
          <div className="w-full py-2">
            {/* Image Preview Grid - Responsive layout */}
            <div key="image-preview-grid" className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-2">
              {imagePreviews.map((preview, index) => (
                <div key={`preview-${preview.id}`} className="relative">
                  <div className="aspect-square overflow-hidden rounded-lg border border-zinc-700">
                    <img
                      key={`preview-img-${preview.id}`}
                      src={preview.url}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation()
                      onRemoveFile(index)
                    }}
                    className={`absolute top-1 right-1 w-6 h-6 ${themeConfig.secondCard} hover:bg-red-500 hover:text-white`}
                  >
                    <FaTrash className="w-3 h-3" />
                  </Button>
                  <p className={`${themeConfig.textMuted} text-[8px] mt-1 truncate`}>
                    {preview.isExisting ? t('existing_photo') : selectedFiles[index - imagePreviews.filter(p => p.isExisting).length]?.name.substring(0, 15)}
                    {!preview.isExisting && selectedFiles[index - imagePreviews.filter(p => p.isExisting).length]?.name.length > 15 ? '...' : ''}
                  </p>
                </div>
              ))}

              {/* Add More Button - Only show if less than max files */}
              {imagePreviews.length < maxFiles && (
                <div
                  className={` ${themeConfig.card} border-2 border-dashed ${themeConfig.border} rounded-lg flex flex-col items-center justify-center aspect-square ${themeConfig.borderHover} transition-colors cursor-pointer`}
                  onClick={(e) => {
                    e.stopPropagation()
                    onAddMoreClick()
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className={`h-8 w-8 ${themeConfig.textMuted} mb-1`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <p className={`${themeConfig.textMuted} text-[10px] text-center`}>{t('add_more')}</p>
                </div>
              )}
            </div>

            <p className={`${themeConfig.textMuted} text-[10px] sm:text-xs text-center`}>
              {imagePreviews.length} {t('of_text')} {maxFiles} {t('photo_count')}
            </p>
          </div>
        ) : (
          <div className="py-4 md:py-3 flex flex-col items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-zinc-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
            </svg>
            <p className="text-zinc-500 text-[10px] sm:text-xs text-center">
              {emptyStateText || t('drop_files_here')}<br/>
              <span className="text-[8px] sm:text-[10px]">{t('max_files')}</span>
            </p>
          </div>
        )}
        <input
          id={fileInputId}
          type="file"
          accept="image/jpeg,image/jpg,image/png"
          className="hidden"
          onChange={onFileChange}
          multiple
        />
      </div>
      {uploadError && (
        <p className="text-red-500 text-[10px] sm:text-xs mt-1">{uploadError}</p>
      )}
    </div>
  )
}