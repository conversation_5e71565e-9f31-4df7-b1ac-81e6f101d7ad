'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useTheme, useThemeConfig } from '@/context/ThemeContext';

interface AudioPlayerProps {
  audioUrl: string;
  className?: string;
  compact?: boolean;
  isSelected?: boolean;
  isPlaying?: boolean;
  onClick?: () => void;
  onError?: (error: string) => void;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioUrl,
  className = '',
  compact = false,
  isSelected = false,
  isPlaying = false,
  onClick,
}) => {
  const { theme } = useTheme();
  const themeConfig = useThemeConfig();
  const audioRef = useRef<HTMLAudioElement>(null);
  
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  // Handle click - delegate to parent component
  const handleClick = () => {
    onClick?.();
  };

  // Handle audio playback state changes
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isSelected && isPlaying) {
      const playPromise = audio.play();
      if (playPromise !== undefined) {
        playPromise.catch((error) => {
          console.warn('Audio play failed:', error);
        });
      }
    } else if (isSelected && !isPlaying) {
      audio.pause();
    } else if (!isSelected) {
      // Reset audio when not selected
      audio.pause();
      audio.currentTime = 0;
      setCurrentTime(0);
    }
  }, [isSelected, isPlaying]);

  // Set up audio events
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration || 0);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime || 0);
    };

    const handleEnded = () => {
      setCurrentTime(0);
      // Auto-reset on end, parent will handle state change
      onClick?.();
    };

    const handleError = () => {
      console.warn('Audio loading error for:', audioUrl);
      setDuration(0);
    };

    // Add event listeners
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
    };
  }, [onClick, audioUrl]);

  // Calculate remaining time
  const timeLeft = duration > 0 ? Math.max(0, Math.floor(duration - currentTime)) : 0;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* HTML5 audio element */}
      <audio 
        ref={audioRef} 
        src={audioUrl} 
        preload="metadata"
        controls={false}
        playsInline={true}
      />

      {/* Play/Pause Button */}
      <button
        onClick={handleClick}
        className={`flex items-center justify-center rounded-full transition-all duration-200 ${
          compact ? 'w-6 h-6' : 'w-8 h-8'
        } ${theme === 'dark' ? 'bg-transparent border border-white text-white hover:bg-jade-purple-dark hover:text-white' : `bg-transparent border ${themeConfig.border} ${themeConfig.text} hover:bg-jade-purple hover:text-white`}`}
      >
        {isSelected && isPlaying ? (
          <svg className={compact ? 'w-3 h-3' : 'w-4 h-4'} fill="currentColor" viewBox="0 0 24 24">
            <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" />
          </svg>
        ) : (
          <svg className={compact ? 'w-3 h-3' : 'w-4 h-4'} fill="currentColor" viewBox="0 0 24 24">
            <path d="M8 5v14l11-7z" />
          </svg>
        )}
      </button>

      {/* Time display */}
      <span className={`text-sm ${themeConfig.textMuted}`}>
        {duration === 0 ? '...' : `${timeLeft}s`}
      </span>
    </div>
  );
};

export default AudioPlayer;