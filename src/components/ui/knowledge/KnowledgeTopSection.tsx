'use client'

import { FaBrain, FaImage } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { LinkButton } from '@/components/ui'

interface KnowledgeTopSectionProps {
  currentPath: string
  totalFaqs: number
  totalFaqsLimit: number | null
  photoCount: number
  photoLimit: number | null
  faqUsagePercentage: number
  photoUsagePercentage: number
  isLoadingCount: boolean
  // Dashboard-specific props for message usage
  messageUsed?: number
  messageLimit?: number
  messageUsagePercentage?: number
  // Comment usage props (placeholder for now)
  commentUsed?: number
  commentLimit?: number
  commentUsagePercentage?: number
  isDashboardView?: boolean
}

export default function KnowledgeTopSection({
  currentPath,
  totalFaqs,
  totalFaqsLimit,
  photoCount,
  photoLimit,
  faqUsagePercentage,
  photoUsagePercentage,
  isLoadingCount,
  messageUsed = 0,
  messageLimit = 0,
  messageUsagePercentage = 0,
  commentUsed = 0,
  commentLimit = 2000,
  commentUsagePercentage = 0,
  isDashboardView = false
}: KnowledgeTopSectionProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Determine active states based on current path
  const isBusinessInsightActive = currentPath === '/dashboard/knowledge' || (isDashboardView && currentPath === '/dashboard')
  const isPhotoGalleryActive = currentPath === '/dashboard/knowledge/photo'
  const isIntrosOutrosActive = currentPath === '/dashboard/knowledge/intro'
  const isConnectActive = currentPath === '/dashboard/connect'

  return (
    <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
      {/* Left Card: Statistics */}
      <div
        className={`relative ${themeConfig.card} rounded-2xl p-3 sm:p-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
        style={theme === 'dark' ? {
          boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
        } : {
          boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
        }}>

        <div className="relative z-10 -mt-1">
          {/* Top Row: Brain Circle + Comment Circle */}
          <div className="flex items-center justify-around mb-1 sm:mb-2">
            {/* Brain Section - Left */}
            <div className="text-center">
              <div className="relative h-10 w-10 sm:h-16 sm:w-16 mx-auto mb-1 sm:mb-2">
                <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                  <defs>
                    <filter id="glow1">
                      <feGaussianBlur stdDeviation="2.5" result="blur" />
                      <feComposite in="SourceGraphic" in2="blur" operator="over" />
                    </filter>
                  </defs>
                  <circle cx="50" cy="50" r="40" fill={themeConfig.statCircleBackground} stroke={themeConfig.statCircleBorder} strokeWidth="3" />
                  <circle cx="50" cy="50" r="40" fill="transparent" stroke="rgb(83, 44, 199)" strokeWidth="6" opacity="0.2" />
                  <circle cx="50" cy="50" r="40" fill="transparent" stroke="rgb(116, 90, 231)" strokeWidth="6"
                    strokeDasharray={`${Math.min((faqUsagePercentage / 100) * 251.2, 251.2)} 251.2`}
                    strokeDashoffset="0" transform="rotate(-90 50 50)" filter="url(#glow1)" />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className={`w-5 h-5 sm:w-8 sm:h-8 rounded-full border ${themeConfig.border} flex items-center justify-center`} style={{
                    boxShadow: '0 4px 12px rgba(116, 85, 184, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1)'
                  }}>
                    <FaBrain className="w-2.5 h-2.5 sm:w-4 sm:h-4 text-jade-purple/75" />
                  </div>
                </div>
              </div>
              <div>
                <p className={`${themeConfig.text} font-semibold font-title`} style={{ fontSize: '11px' }}>Chhlat Brain</p>
                <p className={`${themeConfig.textMuted} font-title`} style={{ fontSize: '10px' }}>
                  {isLoadingCount ? 
                    <span className={`w-2 h-2 border-2 border-current border-t-transparent rounded-full animate-spin ${themeConfig.text}`}></span>
                    : <>{totalFaqs} / {totalFaqsLimit || 0}</>
                  }
                </p>
              </div>
            </div>

            {/* Comment Section - Right */}
            {isDashboardView && (
              <div className="text-center">
                <div className="relative h-10 w-10 sm:h-16 sm:w-16 mx-auto mb-1 sm:mb-2">
                  <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                    <defs>
                      <filter id="glow2">
                        <feGaussianBlur stdDeviation="2.5" result="blur" />
                        <feComposite in="SourceGraphic" in2="blur" operator="over" />
                      </filter>
                    </defs>
                    <circle cx="50" cy="50" r="40" fill={themeConfig.statCircleBackground} stroke={themeConfig.statCircleBorder} strokeWidth="3" />
                    <circle cx="50" cy="50" r="40" fill="transparent" stroke="rgb(83, 44, 199)" strokeWidth="6" opacity="0.2" />
                    <circle cx="50" cy="50" r="40" fill="transparent" stroke="rgb(116, 90, 231)" strokeWidth="6"
                      strokeDasharray={`${Math.min((commentUsagePercentage / 100) * 251.2, 251.2)} 251.2`}
                      strokeDashoffset="0" transform="rotate(-90 50 50)" filter="url(#glow2)" />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className={`w-5 h-5 sm:w-8 sm:h-8 rounded-full border ${themeConfig.border} flex items-center justify-center`} style={{
                      boxShadow: '0 4px 12px rgba(116, 85, 184, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1)'
                    }}>
                      <svg className="w-2.5 h-2.5 sm:w-4 sm:h-4 text-jade-purple/75" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                  </div>
                </div>
                <div>
                  <p className={`${themeConfig.text} font-semibold font-title`} style={{ fontSize: '11px' }}>Comment</p>
                  <p className={`${themeConfig.textMuted} font-title`} style={{ fontSize: '10px' }}>
                    {commentUsed} / {commentLimit}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Bottom Row: Messages (Dashboard View Only) */}
          {isDashboardView && (
            <div className="border-t border-gray-200/20 pt-1 sm:pt-2">
              <div className="flex items-center justify-between mb-2 sm:mb-3">
                <p className={`${themeConfig.text} font-semibold font-title`} style={{ fontSize: '11px' }}>{t('messages')}</p>
                <p className={`${themeConfig.textSecondary} font-title`} style={{ fontSize: '11px' }}>
                  {Math.round(messageUsagePercentage)}%
                </p>
              </div>
              <div className={`h-3 sm:h-4 ${themeConfig.interactive} rounded-full overflow-hidden relative`}>
                <div className="h-full bg-gradient-to-r from-dashboard-primary to-purple-600 rounded-full transition-all duration-200"
                  style={{ width: `${Math.min(messageUsagePercentage, 100)}%` }}></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs font-semibold text-white" style={{ fontSize: '9px' }}>
                    {messageUsed} / {messageLimit}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Card: Action Buttons */}
      <div
        className={`relative ${themeConfig.card} rounded-2xl p-3 sm:p-6 border ${themeConfig.border} ${themeConfig.borderHover} transition-all duration-300 group overflow-hidden`}
        style={theme === 'dark' ? {
          boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
        } : {
          boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
        }}>

        <div className="relative z-10">
          {/* Buttons Grid */}
          <div className="grid grid-cols-1 gap-2 sm:gap-3">
            <LinkButton
              href="/dashboard/knowledge"
              variant={isBusinessInsightActive ? "primary" : "secondary"}
              size="md"
              className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
              isActive={isBusinessInsightActive}
            >
              {t('business_insight')}
            </LinkButton>

            <LinkButton
              href="/dashboard/knowledge/intro"
              variant={isIntrosOutrosActive ? "primary" : "secondary"}
              size="md"
              className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
              isActive={isIntrosOutrosActive}
            >
              {t('intros_outros')}
            </LinkButton>

            <LinkButton
              href="/dashboard/connect"
              variant={isConnectActive ? "primary" : "secondary"}
              size="md"
              className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
              isActive={isConnectActive}
            >
              {t('connects')}
            </LinkButton>
          </div>
        </div>
      </div>
    </div>
  )
}