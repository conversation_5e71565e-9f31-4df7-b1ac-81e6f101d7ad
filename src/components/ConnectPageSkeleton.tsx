'use client'

import { useThemeConfig } from '@/context/ThemeContext'

export default function ConnectPageSkeleton() {
  const themeConfig = useThemeConfig()

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between mb-8">
        <div className={`h-8 w-16 ${themeConfig.skeletonElement} rounded`}></div>
        <div className={`h-8 w-48 ${themeConfig.skeletonElement} rounded`}></div>
        <div className="w-10"></div>
      </div>

      {/* Connected Platforms Card */}
      <div className={`${themeConfig.skeletonCard} rounded-2xl p-6 border`}>
        <div className="relative z-10">
          {/* Card Header */}
          <div className="flex justify-between items-center mb-4">
            <div>
              <div className={`h-6 w-48 ${themeConfig.skeletonElement} rounded mb-2`}></div>
            </div>
            <div className={`h-6 w-12 ${themeConfig.skeletonElement} rounded`}></div>
          </div>

          {/* Connected Platforms Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
            {[...Array(2)].map((_, index) => (
              <div
                key={index}
                className={`${themeConfig.skeletonCard} border rounded-lg p-4`}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${themeConfig.skeletonElement} rounded-full mr-4`}></div>
                    <div>
                      <div className={`h-4 w-32 ${themeConfig.skeletonElement} rounded mb-2`}></div>
                      <div className={`h-3 w-24 ${themeConfig.skeletonElement} rounded`}></div>
                    </div>
                  </div>
                  <div className={`w-12 h-6 ${themeConfig.skeletonElement} rounded-full`}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Platform Selector Card */}
      <div className={`${themeConfig.skeletonCard} rounded-2xl p-6 border`}>
        <div className="relative z-10">
          <div className={`h-6 w-40 ${themeConfig.skeletonElement} rounded mb-4`}></div>
          <div className={`h-12 w-full ${themeConfig.skeletonElement} rounded-lg`}></div>
        </div>
      </div>

      {/* Selected Platform Form Card */}
      <div className={`${themeConfig.skeletonCard} rounded-2xl p-6 border`}>
        <div className="relative z-10">
          {/* Platform Header */}
          <div className="flex items-center mb-6">
            <div className={`w-12 h-12 ${themeConfig.skeletonElement} rounded-full mr-4`}></div>
            <div style={{ flex: 1 }}>
              <div className={`h-6 w-48 ${themeConfig.skeletonElement} rounded mb-2`}></div>
              <div className={`h-4 w-64 ${themeConfig.skeletonElement} rounded`}></div>
            </div>
          </div>

          {/* Form Fields */}
          <div className="space-y-3">
            {/* URL Fields Row */}
            <div className="flex flex-col sm:flex-row gap-3">
              <div className={`w-full sm:w-[40%] h-10 ${themeConfig.skeletonElement} rounded-lg`}></div>
              <div className={`flex-1 h-10 ${themeConfig.skeletonElement} rounded-lg`}></div>
            </div>

            {/* Input and Button Row */}
            <div className="flex flex-col sm:flex-row gap-3">
              <div className={`flex-1 h-10 ${themeConfig.skeletonElement} rounded-lg`}></div>
              <div className={`w-24 h-10 ${themeConfig.skeletonElement} rounded-lg`}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}