'use client'

import { useThemeConfig } from '@/context/ThemeContext'

export default function ThemeAwareSkeleton() {
  const themeConfig = useThemeConfig()

  return (
    <div className="space-y-6">
      {/* Business Insights Section Loading - matches actual dashboard layout */}
      <div className={`${themeConfig.skeletonCard} rounded-2xl p-6 border ${themeConfig.border}`}>
        {/* Header section */}
        <div className="flex justify-between items-center mb-6">
          <div className={`h-6 ${themeConfig.skeletonElement} rounded w-48`}></div>
          <div className={`h-8 ${themeConfig.skeletonElement} rounded w-20`}></div>
        </div>

        {/* Help section */}
        <div className={`${themeConfig.skeletonElement} rounded-lg h-16 mb-6`}></div>

        {/* Input row */}
        <div className="grid grid-cols-1 sm:grid-cols-12 gap-3 mb-6">
          <div className="sm:col-span-5">
            <div className={`h-11 ${themeConfig.skeletonElement} rounded-lg`}></div>
          </div>
          <div className="sm:col-span-5">
            <div className={`h-11 ${themeConfig.skeletonElement} rounded-lg`}></div>
          </div>
          <div className="sm:col-span-2">
            <div className={`h-11 ${themeConfig.skeletonElement} rounded-lg`}></div>
          </div>
        </div>

        {/* Recently Added section */}
        <div className={`border-t ${themeConfig.divider} pt-6`}>
          <div className="flex justify-between items-center mb-3">
            <div className={`h-5 ${themeConfig.skeletonElement} rounded w-32`}></div>
            <div className={`h-8 ${themeConfig.skeletonElement} rounded w-16`}></div>
          </div>

          {/* Table headers */}
          <div className={`flex border-b ${themeConfig.divider} py-3`}>
            <div className="w-[5%] px-2">
              <div className={`h-4 ${themeConfig.skeletonElement} rounded w-4`}></div>
            </div>
            <div className="w-[35%] px-2">
              <div className={`h-4 ${themeConfig.skeletonElement} rounded w-16`}></div>
            </div>
            <div className="w-[35%] px-2">
              <div className={`h-4 ${themeConfig.skeletonElement} rounded w-12`}></div>
            </div>
            <div className="w-[25%] px-2"></div>
          </div>

          {/* Empty state message */}
          <div className="py-8 text-center">
            <div className={`h-4 ${themeConfig.skeletonElement} rounded w-64 mx-auto`}></div>
          </div>
        </div>
      </div>
    </div>
  )
}