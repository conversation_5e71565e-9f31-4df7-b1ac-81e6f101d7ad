CREATE TABLE public.chat_histories (
  id SERIAL NOT NULL,
  client_id text NOT NULL,
  customer_id TEXT NOT NULL,
  platform TEXT NOT NULL,
  user_message TEXT NULL,
  cleaned_question TEXT NULL,
  normalized_question TEXT NULL,
  bot_message TEXT NULL,
  question TEXT NULL,
  answer TEXT NULL,
  question_p TEXT NULL,
  answer_p TEXT NULL,
  flag TEXT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  rag_matched BOOLEAN NULL,
  rag_score REAL NULL,
  chat_type TEXT NULL,
  sub_flag TEXT NULL,
  lang TEXT NULL,
  respond_lang TEXT NULL,
  tg_name TEXT NULL,
  tg_id TEXT NULL,
  audio_duration INTEGER NULL,
  sector TEXT NULL,
  button_clicked INTEGER NULL,
  feedback_score INTEGER NULL,
  photo_url TEXT[] NULL,
  answer_audio_duration INTEGER NULL,
  run_time DOUBLE PRECISION NULL,
  
  CONSTRAINT chat_histories_pkey PRIMARY KEY (id),
  CONSTRAINT chat_histories_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

-- Essential indexes for performance
CREATE INDEX IF NOT EXISTS idx_chat_histories_client_id ON public.chat_histories (client_id);
CREATE INDEX IF NOT EXISTS idx_chat_histories_client_customer ON public.chat_histories (client_id, customer_id);
-- Additional useful indexes for common queries
CREATE INDEX IF NOT EXISTS idx_chat_histories_created_at ON public.chat_histories (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_chat_histories_platform ON public.chat_histories (platform);