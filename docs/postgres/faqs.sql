-- Enable vector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create FAQs table
CREATE TABLE public.faqs (
  id SERIAL NOT NULL,
  client_id text NOT NULL,
  question TEXT NULL,
  question_p TEXT NULL,
  answer TEXT NULL,
  answer_p TEXT NULL,
  audio_url TEXT NULL,
  photo_url text[] NULL,
  photo_id TEXT NULL,
  audio_duration INTEGER NULL,
  audio_file_path TEXT NULL,
  updated_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  faq_id TEXT NULL,
  fb_photo_atmid text[] NULL,
  fb_audio_atmid TEXT NULL,
  tg_photo_atmid text[] NULL,
  tg_audio_atmid TEXT NULL,
  ig_photo_atmid text[] NULL,
  ig_audio_atmid TEXT NULL,
  is_visible BOOLEAN NULL,
  answer_summary TEXT NULL,
  embedding VECTOR(1536) NULL,
  q_embedding VECTOR(768) NULL,
  
  CONSTRAINT faqs_pkey PRIMARY KEY (id),
  CONSTRAINT faqs_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_faqs_client_id ON public.faqs (client_id);
-- CREATE INDEX IF NOT EXISTS idx_faqs_client_visible ON public.faqs (client_id, is_visible) WHERE is_visible = true;
-- Vector similarity search index (ESSENTIAL for performance)
-- CREATE INDEX IF NOT EXISTS idx_faqs_embedding ON public.faqs USING hnsw (embedding vector_cosine_ops);

-- Add trigger for auto-updating updated_at
-- CREATE TRIGGER trg_update_faqs_timestamp 
--   BEFORE UPDATE ON public.faqs 
--   FOR EACH ROW 
--   EXECUTE FUNCTION public.update_modified_column();




-- =====================================================
-- OPTIMIZED RAG SEARCH FUNCTION Q
-- =====================================================

DROP FUNCTION IF EXISTS public.fetch_similar_q(vector(256), text, integer);

ALTER TABLE public.faqs DROP COLUMN q_embedding;
ALTER TABLE public.faqs ADD COLUMN q_embedding VECTOR(768);

CREATE OR REPLACE FUNCTION public.fetch_similar_q(
  query_embedding vector(768),
  p_client_id text,
  match_count INT DEFAULT 15
)
RETURNS TABLE (
  id INT,
  faq_id TEXT,
  similarity FLOAT,
  question TEXT,
  answer TEXT,
  answer_summary TEXT,
  question_p TEXT,
  answer_p TEXT,
  audio_url TEXT,
  photo_url text[],
  audio_duration INT,
  fb_photo_atmid text[],
  fb_audio_atmid TEXT,
  tg_photo_atmid text[],
  tg_audio_atmid TEXT,
  ig_photo_atmid text[],
  ig_audio_atmid TEXT
)
LANGUAGE plpgsql
STABLE
PARALLEL SAFE
AS $$
BEGIN
  RETURN QUERY
    SELECT
      f.id,
      f.faq_id,
      (1 - (f.q_embedding <=> query_embedding))::FLOAT AS similarity,
      f.question,
      f.answer,
      f.answer_summary,
      f.question_p,
      f.answer_p,
      f.audio_url,
      f.photo_url,
      f.audio_duration,
      f.fb_photo_atmid,
      f.fb_audio_atmid,
      f.tg_photo_atmid,
      f.tg_audio_atmid,
      f.ig_photo_atmid,
      f.ig_audio_atmid
    FROM public.faqs f
    WHERE f.client_id = p_client_id
      AND f.q_embedding IS NOT NULL
    ORDER BY f.q_embedding <=> query_embedding
    LIMIT match_count;
END;
$$;


-- =====================================================
-- OPTIMIZED RAG SEARCH FUNCTION
-- =====================================================


DROP FUNCTION IF EXISTS public.fetch_similar_faqs(vector(1536), text, integer);
DROP VIEW IF EXISTS public.faq_embedding_stats;

CREATE OR REPLACE FUNCTION public.fetch_similar_faqs(
  query_embedding vector(1536),
  p_client_id text,
  match_count INT DEFAULT 15
)
RETURNS TABLE (
  id INT,
  faq_id TEXT,
  similarity FLOAT,
  question TEXT,
  answer TEXT,
  answer_summary TEXT,
  question_p TEXT,
  answer_p TEXT,
  audio_url TEXT,
  photo_url text[],
  audio_duration INT,
  fb_photo_atmid text[],
  fb_audio_atmid TEXT,
  tg_photo_atmid text[],
  tg_audio_atmid TEXT,
  ig_photo_atmid text[],
  ig_audio_atmid TEXT
)
LANGUAGE plpgsql
STABLE
PARALLEL SAFE
AS $$
BEGIN
  RETURN QUERY
    SELECT
      f.id,
      f.faq_id,
      (1 - (f.embedding <=> query_embedding))::FLOAT AS similarity,
      f.question,
      f.answer,
      f.answer_summary,
      f.question_p,
      f.answer_p,
      f.audio_url,
      f.photo_url,
      f.audio_duration,
      f.fb_photo_atmid,
      f.fb_audio_atmid,
      f.tg_photo_atmid,
      f.tg_audio_atmid,
      f.ig_photo_atmid,
      f.ig_audio_atmid
    FROM public.faqs f
    WHERE f.client_id = p_client_id
      AND f.embedding IS NOT NULL
    ORDER BY f.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- =====================================================
-- PERFORMANCE MONITORING VIEW
-- =====================================================

CREATE OR REPLACE VIEW public.faq_embedding_stats AS
SELECT 
  client_id,
  COUNT(*) as total_faqs,
  COUNT(embedding) as faqs_with_embedding,
  COUNT(CASE WHEN is_visible THEN 1 END) as visible_faqs,
  COUNT(CASE WHEN NOT is_visible THEN 1 END) as hidden_variants,
  ROUND(COUNT(embedding)::NUMERIC / COUNT(*) * 100, 2) as embedding_coverage_pct
FROM public.faqs
GROUP BY client_id;