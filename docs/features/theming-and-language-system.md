# Theming and Language System

This document provides comprehensive guidance for implementing consistent theming and language switching across projects based on the Chhlat Bot implementation.

## Theme System Architecture

### Context Structure

```typescript
// src/context/ThemeContext.tsx
export type Theme = 'dark' | 'light'

interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
  toggleTheme: () => void
}
```

### Core Implementation Pattern

```typescript
// Theme Provider Setup
export function ThemeProvider({ children }: { children: ReactNode }) {
  const [theme, setThemeState] = useState<Theme>('dark')
  
  // Persistence: localStorage + cookie fallback + HTML class
  useEffect(() => {
    // Load from localStorage -> cookie -> default
    const savedTheme = localStorage.getItem('uiTheme') as Theme
    if (savedTheme && (savedTheme === 'dark' || savedTheme === 'light')) {
      setThemeState(savedTheme)
      document.documentElement.classList.add(`${savedTheme}-theme`)
    }
  }, [])
  
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme)
    localStorage.setItem('uiTheme', newTheme)
    document.cookie = `uiTheme=${newTheme}; path=/; max-age=31536000; SameSite=Lax`
    
    // Apply theme class to HTML element
    const htmlEl = document.documentElement
    htmlEl.className = htmlEl.className.replace(/\b(light|dark)(-theme)?\b/g, '')
    htmlEl.classList.add(`${newTheme}-theme`)
  }
}
```

## Color System Specification

### Tailwind Configuration

```javascript
// tailwind.config.js
colors: {
  // Brand Colors
  'jade-purple': '#6135E6',
  'jade-purple-dark': '#532CC7', 
  'jade-purple-light': '#E1E0FF',
  
  // Dashboard Theme System
  'dashboard': {
    // Dark Theme Colors
    'dark-bg': '#01001c',                    // Main background
    'dark-surface': '#0F0F2A',               // Card backgrounds
    'dark-surface-dark': '#18183A',          // Secondary surfaces
    'dark-text-primary': '#D1D5DB',          // Primary text
    'dark-text-secondary': '#C2C6CC',        // Secondary text
    'dark-text-muted': '#D1D5DB',            // Muted text
    'dark-border-default': '#333366',        // Default borders
    'dark-border-hover': '#7672E8',          // Hover borders
    'dark-interactive': '#18183A',           // Interactive elements
    'dark-interactive-hover': '#0F0F2A',     // Interactive hover
    
    // Light Theme Colors
    'light-bg': '#F5F7FB',                   // Main background
    'light-surface': '#FFFFFF',              // Card backgrounds  
    'light-surface-dark': '#FAFAFA',         // Secondary surfaces
    'light-text-primary': '#4B5563',         // Primary text
    'light-text-secondary': '#4B5563',       // Secondary text
    'light-text-muted': '#374151',           // Muted text
    'light-border-default': '#D3DCE5',       // Default borders
    'light-border-hover': '#B8C5D1',         // Hover borders
    'light-interactive': '#F6F6F6',          // Interactive elements
    'light-interactive-hover': '#F5F7FB',    // Interactive hover
    
    // Semantic Colors (Shared)
    'primary': '#532CC7',                    // Primary brand
    'success': '#22C55E',                    // Success states
    'error': '#EF4444',                      // Error states
    'warning': '#F59E0B',                    // Warning states
    'info': '#3B82F6',                       // Info states
  }
}
```

### Theme Configuration Object

```typescript
// Theme configuration provides pre-built class combinations
export const themes = {
  dark: {
    // Page Structure
    pageBackground: 'min-h-screen flex flex-col relative bg-dashboard-dark-bg',
    backgroundEffects: (
      <>
        <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
        <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-dashboard-primary/5 rounded-full blur-[120px] -z-10"></div>
      </>
    ),
    
    // Surface Elements
    card: '',                                                    // Transparent glass-morphism cards
    secondCard: 'bg-dashboard-dark-surface',                     // Secondary surfaces
    interactive: 'bg-dashboard-dark-interactive',                // Interactive backgrounds
    interactiveDark: 'bg-dashboard-dark-surface-dark',          // Darker interactive
    
    // Border System
    border: 'border-dashboard-dark-border-default',              // Default borders
    borderHover: 'md:hover:border-dashboard-dark-border-hover',  // Hover states
    
    // Typography System
    text: 'text-dashboard-dark-text-primary',                    // Primary text
    textSecondary: 'text-dashboard-dark-text-secondary',         // Secondary text
    textMuted: 'text-dashboard-dark-text-muted',                 // Muted text
    
    // Interactive States
    interactiveHover: 'md:hover:bg-dashboard-dark-interactive-hover',
    
    // Brand Assets
    logo: '/images/white_tran_logo.svg',                         // Dark theme logo
    
    // Component Styling
    skeletonCard: 'bg-dashboard-dark-surface border-dashboard-dark-border-default',
    skeletonElement: 'bg-dashboard-dark-surface-dark',
  },
  
  light: {
    // Page Structure
    pageBackground: 'min-h-screen flex flex-col relative bg-dashboard-light-bg',
    backgroundEffects: null,                                     // No effects for light theme
    
    // Surface Elements
    card: 'bg-dashboard-light-surface',                          // Solid white cards
    secondCard: 'bg-dashboard-light-surface-dark',               // Light gray surfaces
    interactive: 'bg-dashboard-light-interactive-hover',         // Interactive backgrounds
    interactiveDark: 'bg-dashboard-light-interactive',           // Darker interactive
    
    // Border System
    border: 'border-dashboard-light-border-default',             // Default borders
    borderHover: 'md:hover:border-dashboard-light-border-hover', // Hover states
    
    // Typography System
    text: 'text-dashboard-light-text-primary',                   // Primary text
    textSecondary: 'text-dashboard-light-text-secondary',        // Secondary text
    textMuted: 'text-dashboard-light-text-muted',                // Muted text
    
    // Interactive States
    interactiveHover: 'md:hover:bg-jade-purple-light',
    
    // Brand Assets
    logo: '/images/purple_tran_logo.svg',                        // Light theme logo
    
    // Component Styling
    skeletonCard: 'bg-dashboard-light-surface border-dashboard-light-border-default',
    skeletonElement: 'bg-dashboard-light-bg',
  }
}
```

### Usage Patterns

```typescript
// Component Implementation
export default function MyComponent() {
  const { theme, toggleTheme } = useTheme()
  const themeConfig = useThemeConfig()
  
  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects for dark theme */}
      {themeConfig.backgroundEffects}
      
      {/* Theme-aware card */}
      <div 
        className={`${themeConfig.card} rounded-2xl p-6 border ${themeConfig.border}`}
        style={theme === 'dark' ? {
          boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
        } : {
          boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
        }}
      >
        <h2 className={`${themeConfig.text} font-title`}>Content</h2>
        <p className={`${themeConfig.textSecondary}`}>Description</p>
        
        {/* Theme toggle button */}
        <button
          onClick={toggleTheme}
          className={`${themeConfig.interactiveDark} border ${themeConfig.border} ${themeConfig.interactiveHover} transition-all duration-300`}
        >
          {theme === 'dark' ? <SunIcon /> : <MoonIcon />}
        </button>
      </div>
    </div>
  )
}
```

## Language System Architecture

### Context Structure

```typescript
// src/context/LanguageContext.tsx
export type Language = 'km' | 'en'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}
```

### Dynamic Translation Loading

```typescript
// Language Provider Implementation
export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguageState] = useState<Language>(() => detectBrowserLanguage())
  const [translations, setTranslations] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(true)

  // Dynamic translation loading
  const loadTranslations = async (lang: Language) => {
    try {
      setIsLoading(true)
      const translationModule = await import(`../locales/${lang}.json`)
      setTranslations(translationModule.default || translationModule)
      setIsLoading(false)
    } catch (error) {
      // Fallback to English
      if (lang !== 'en') {
        const fallbackModule = await import('../locales/en.json')
        setTranslations(fallbackModule.default || fallbackModule)
      }
      setIsLoading(false)
    }
  }

  // Browser language detection
  const detectBrowserLanguage = (): Language => {
    if (typeof window === 'undefined') return 'en'
    const browserLang = navigator.language.toLowerCase()
    if (browserLang.startsWith('km')) return 'km'
    return 'en' // Default to English
  }

  // Persistence: localStorage + cookie + HTML lang attribute
  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    if (typeof window !== 'undefined') {
      localStorage.setItem('uiLanguage', lang)
      document.documentElement.lang = lang
      document.cookie = `uiLanguage=${lang}; path=/; max-age=31536000; SameSite=Lax`
    }
  }

  // Translation function
  const t = (key: string): string => {
    if (isLoading) return key  // Return key while loading
    return translations[key] || key  // Fallback to key if not found
  }
}
```

### Translation File Structure

```json
// src/locales/en.json
{
  "dashboard": "Dashboard",
  "messages": "Messages",
  "plan": "Plan",
  "ai_brain": "Chhlat Brain",
  "connects": "Connects",
  "upgrade": "Upgrade",
  "changing_language": "Changing language...",
  
  // Error messages with placeholders
  "enter_valid_token": "Please enter a valid access token for {platform}.",
  "connection_limit_reached_error": "You've reached your connection limit ({limit}). Please upgrade your plan."
}

// src/locales/km.json  
{
  "dashboard": "ផ្ទាំងគ្រប់គ្រង",
  "messages": "ចំនួនសារ", 
  "plan": "គម្រោង",
  "ai_brain": "Chhlat Brain",
  "connects": "ភ្ជាប់",
  "upgrade": "Upgrade",
  "changing_language": "កំពុងផ្លាស់ប្តូរភាសា...",
  
  // Error messages with placeholders
  "enter_valid_token": "សូមបញ្ចូល access token ត្រឹមត្រូវសម្រាប់ {platform}",
  "connection_limit_reached_error": "អ្នកបានឈានដល់ដែនកំណត់នៃការតភ្ជាប់ ({limit}) សូមដំឡើងគម្រោងរបស់អ្នក"
}
```

### Language Switcher Component

```typescript
// src/components/DashboardLanguageSwitcher.tsx
export default function DashboardLanguageSwitcher() {
  const { language, setLanguage, t } = useLanguage()
  const { showLoading, hideLoading } = useLoading()
  const themeConfig = useThemeConfig()
  const [isChangingLanguage, setIsChangingLanguage] = useState(false)

  // Page reload after language change
  useEffect(() => {
    if (isChangingLanguage) {
      const reloadTimeout = setTimeout(() => {
        window.location.href = window.location.href.split('#')[0]
      }, 500) // Show loading animation first
      return () => clearTimeout(reloadTimeout)
    }
  }, [isChangingLanguage])

  const toggleLanguage = () => {
    showLoading(t('changing_language'))
    const newLang = language === 'en' ? 'km' : 'en'
    
    setTimeout(() => {
      setLanguage(newLang)
      setIsChangingLanguage(true)
    }, 100)
  }

  return (
    <button
      onClick={toggleLanguage}
      className={`w-8 h-8 sm:w-9 sm:h-9 ${themeConfig.interactiveDark} border ${themeConfig.border} rounded-lg flex items-center justify-center ${themeConfig.textSecondary} ${themeConfig.interactiveHover} ${themeConfig.borderHover} transition-all duration-300`}
      title={language === 'en' ? 'ខ្មែរ' : 'English'}
    >
      {language === 'en' ? (
        <span className="text-xs font-semibold">ខ្មែរ</span>
      ) : (
        <span className="text-xs font-semibold">EN</span>
      )}
    </button>
  )
}
```

## Implementation Best Practices

### 1. Theme-Aware Components

Always use `useThemeConfig()` for styling and conditional theme logic:

```typescript
// ✅ Good - Using theme configuration
const themeConfig = useThemeConfig()
return <div className={`${themeConfig.card} ${themeConfig.border}`} />

// ❌ Bad - Hardcoded theme classes
return <div className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700" />
```

### 2. Consistent Color Usage

Use semantic color names from the dashboard color system:

```typescript
// ✅ Good - Semantic color usage
'text-dashboard-dark-text-primary'
'bg-dashboard-light-surface'
'border-dashboard-dark-border-hover'

// ❌ Bad - Generic color names
'text-gray-200'
'bg-gray-900'
'border-gray-600'
```

### 3. Translation Key Naming

Use consistent, hierarchical naming for translation keys:

```json
{
  // Feature-based grouping
  "dashboard_header": "Dashboard",
  "dashboard_welcome": "Welcome",
  
  // Action-based grouping  
  "button_save": "Save",
  "button_cancel": "Cancel",
  
  // Error messages with context
  "error_connection_failed": "Connection failed",
  "error_invalid_token": "Invalid token provided",
  
  // Status messages
  "status_loading": "Loading...",
  "status_success": "Success!"
}
```

### 4. Component Styling Patterns

```typescript
// Card component pattern
<div 
  className={`${themeConfig.card} rounded-2xl p-6 border ${themeConfig.border} ${themeConfig.borderHover} transition-all duration-300`}
  style={theme === 'dark' ? {
    boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
  } : {
    boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
  }}
>

// Interactive button pattern
<button
  className={`${themeConfig.interactiveDark} border ${themeConfig.border} ${themeConfig.interactiveHover} ${themeConfig.borderHover} transition-all duration-300`}
>

// Text hierarchy pattern
<h1 className={`${themeConfig.text} font-title`}>Primary Heading</h1>
<h2 className={`${themeConfig.textSecondary} font-title`}>Secondary Heading</h2>
<p className={`${themeConfig.textMuted}`}>Body text</p>
```

### 5. Loading States and Skeletons

Create theme-aware loading components:

```typescript
export default function ThemeAwareSkeleton() {
  const themeConfig = useThemeConfig()
  
  return (
    <div className={`${themeConfig.skeletonCard} rounded-xl p-4 animate-pulse`}>
      <div className={`h-8 ${themeConfig.skeletonElement} rounded w-1/3 mb-4`}></div>
      <div className={`h-6 ${themeConfig.skeletonElement} rounded w-2/3`}></div>
    </div>
  )
}
```

## Migration Checklist

When implementing this system in a new project:

### Setup Phase
- [ ] Install required dependencies (React Context, Framer Motion if needed)
- [ ] Copy theme color definitions to `tailwind.config.js`
- [ ] Create `src/context/ThemeContext.tsx` and `src/context/LanguageContext.tsx`
- [ ] Create `src/locales/` directory with translation files
- [ ] Add theme and language providers to app root

### Implementation Phase
- [ ] Create theme-aware components using `useThemeConfig()`
- [ ] Implement language switcher component
- [ ] Add theme toggle functionality 
- [ ] Create loading states and skeletons
- [ ] Set up translation keys and implement `t()` function usage

### Testing Phase
- [ ] Test theme switching persistence across page reloads
- [ ] Test language switching with page reload
- [ ] Verify browser language detection works
- [ ] Test localStorage and cookie fallbacks
- [ ] Verify all components render correctly in both themes
- [ ] Test responsive behavior of theme-aware components

### Quality Assurance
- [ ] Ensure consistent color usage across components
- [ ] Verify proper contrast ratios for accessibility
- [ ] Test with reduced motion preferences
- [ ] Validate translation completeness
- [ ] Performance test with large translation files

This system provides a robust, scalable foundation for consistent theming and internationalization across multiple projects while maintaining excellent user experience and developer productivity.