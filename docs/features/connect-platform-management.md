# Connect Platform Management System

Manages platform connections for Facebook Messenger, Instagram DM, Telegram, and Web API with plan-based limits and real-time validation.

## System Overview

### Core Features
- **Platform Connections**: Facebook, Instagram, Telegram (Bot/Business), Web API
- **Connection Limits**: Plan-based limits (Intern: 1, Assistant: 3, Manager: 4, Free: 1)
- **Status Management**: Enable/disable existing platform connections
- **Real-time Validation**: Token validation, connection limits, billing checks
- **Multi-Modal UI**: Confirmation dialogs, loading states, error handling
- **Webhook Management**: Copy webhook URLs and configuration data

### Architecture
- **Data Source**: Direct connection APIs with dashboard cache fallback for plan data
- **Authentication**: Server-side `verifyAuth()` with client ID validation
- **Database**: Direct PostgreSQL queries for data fetching + N8N webhooks for platform operations
- **State Management**: Complex multi-state coordination with optimistic updates
- **Caching**: Dashboard cache for plan data, no caching for connection-specific data

## Platform Support

### Facebook Messenger
- **Connection**: Access token + webhook URL
- **Validation**: Token verification via Meta Graph API
- **API**: `/api/platform/connect` with `type: 'facebook'`

### Instagram DM
- **Connection**: Access token + Page ID + webhook URL
- **Validation**: Token and Page ID verification
- **API**: `/api/platform/connect` with `type: 'instagram/{page_id}'`

### Telegram
- **Bot Mode**: Bot token + webhook URL
- **Business Mode**: Username (@username) + webhook URL
- **API**: `/api/platform/connect` with `type: 'telegram'` or `'telegram_biz'`

### Web API
- **Connection**: Domain + webhook URL
- **Status**: Coming soon
- **API**: `/api/platform/connect` with `type: 'web'`

## User Flow

### Page Load Sequence
1. **Authentication**: `useAuth()` context provides user session
2. **Data Fetch**: `fetchConnectionData()` calls `/api/connection-data`
3. **State Setup**: Updates connected platforms, limits, and available options
4. **UI Render**: Shows connected platforms grid and platform selector

### Platform Connection Flow
1. **Platform Selection**: User selects from available platforms dropdown
2. **Form Completion**: User fills platform-specific credentials
3. **Validation**: Client-side validation (required fields, format)
4. **Confirmation**: Shows confirmation modal with connection details
5. **API Call**: `handleConnect()` → `/api/platform/connect`
6. **Response**: Success reloads page, error shows overlay message

### Platform Toggle Flow
1. **Toggle Action**: User clicks enable/disable toggle on platform card
2. **Confirmation**: Shows appropriate modal (enable/disable)
3. **Validation**: Server-side billing and usage validation
4. **API Call**: `handleTogglePlatformStatus()` → `/api/platform/toggle`
5. **State Update**: Updates local state and shows success/error overlay

## State Management

### Connection State
- **Connected Platforms**: Boolean map tracking connection status
- **Platform Status**: Boolean map tracking enabled/disabled state
- **Connection Limits**: Plan-based limits with real-time validation

### Modal State Coordination
- **Connect Confirmation**: Platform-specific connection confirmation
- **Enable/Disable Confirmation**: Separate modals for status changes
- **Loading States**: Modal-specific loading states to prevent conflicts

### Form Management
- **Ref-Based Handling**: Input field refs for secure token handling
- **Platform-Specific Validation**: Different validation rules per platform
- **Token Security**: Masked display, no state storage, secure transmission

## API Endpoints

### GET `/api/connection-data` - Connection Data & Limits
**Purpose**: Fetch connection limits and platform credentials
**Features**:
- Plan-based connection limits (Intern: 1, Assistant: 3, Manager: 4, Free: 1)
- Sanitized credentials (no sensitive tokens exposed)
- Cache-first strategy for `plan_type` using `dashboard_${authId}`
- Direct PostgreSQL queries for connection credentials
- Single optimized query with JOIN for client and credentials data

### POST `/api/platform/connect` - Platform Connection
**Purpose**: Handle new platform connections with limit validation
**Features**:
- Connection limit validation using dashboard cache + direct PostgreSQL fallback
- Direct PostgreSQL queries for credentials and platform status validation
- Platform-specific handling (telegram_biz uses @username)
- Token validation (required except for telegram_biz)
- Webhook URL management from stored credentials
- N8N webhook for final connection operation

**Error Codes**:
- `INVALID_TOKEN`: Token validation failed
- `ALREADY_USE_FOR_TRIAL`: Token already used for trial
- `STILL_CONNECTED`: Platform already connected
- `CONNECTION_ERROR`: Connection failed

### POST `/api/platform/toggle` - Platform Status Toggle
**Purpose**: Enable/disable platform connections with validation
**Features**:
- Dashboard cache for `plan_type` + direct PostgreSQL fallback
- Direct PostgreSQL queries for billing and usage validation
- Billing date validation (Phnom Penh timezone)
- Usage limit validation before enabling
- Platform validation against allowed list
- N8N webhook for final toggle operation

### POST `/api/webhook-url` - Webhook URL Retrieval
**Purpose**: Retrieve platform-specific webhook URLs
**Features**:
- Platform-specific URL mapping
- Fallback URLs for each platform
- Special handling for privacy policy URL

## UI Components

### Platform Cards
- **Connection Status**: Visual indicators for connected/disconnected state
- **Enable/Disable Toggle**: Status management with confirmation dialogs
- **Platform Names**: Display connected platform names (fb_name, ig_name, etc.)
- **Connection Count**: Real-time display of connections vs limits

### Platform Selector
- **Dynamic Filtering**: Only show available platforms based on connection limits
- **Dropdown Interface**: Clean selection interface with platform icons
- **Validation**: Prevent selection when limits reached

### Connection Forms
- **Platform-Specific Fields**: Different input fields per platform
- **Real-time Validation**: Instant feedback on form completion
- **Webhook Copy**: One-click webhook URL copying with feedback

## Performance Optimizations

### Caching Strategy
- **Dashboard Cache**: Primary source for `plan_type` information with 30-minute TTL
- **No Connection Caching**: Connection data fetched fresh via direct PostgreSQL queries
- **Optimistic Updates**: Immediate UI feedback with error recovery

### UX Enhancements
- **Body Scroll Management**: Prevent scroll when modals are open
- **Auto-dismiss Messages**: Success/error messages with timers
- **Client-side Hydration**: Prevent SSR hydration issues
- **Loading States**: Granular loading states for better UX

### Error Handling
- **Error Code Mapping**: Backend error codes to user-friendly messages
- **Validation Recovery**: Client-side validation before API calls
- **Network Recovery**: Retry mechanisms for failed requests

## Security Implementation

### Authentication & Authorization
- **Server-side Auth**: `verifyAuth()` on all API endpoints
- **Client-side Context**: `useAuth()` for UI state management
- **JWT Tokens**: Secure webhook authentication
- **User-scoped Operations**: All queries filtered by client ID

### Data Security
- **Sanitized Responses**: No sensitive tokens in API responses
- **Token Handling**: Secure token storage and transmission
- **Input Validation**: Platform-specific credential validation
- **Webhook Verification**: Endpoint validation and security

## EditConnection Page (Disconnect Management)

### Overview
**Location**: `/dashboard/connect/editConnection`  
**Purpose**: Specialized page for viewing and disconnecting existing platform connections

### Core Functionality
- **Display Connected Platforms**: Shows only platforms that are currently connected
- **Disconnect Management**: Allows users to disconnect platforms with confirmation
- **Auto-redirect**: Redirects to main connect page if no platforms are connected
- **Connection Type Display**: Shows Telegram connection type (Bot vs Business)

### User Flow
1. **Page Load**: Calls `/api/connection-data/connected` to fetch connected platforms
2. **Validation**: Redirects to `/dashboard/connect` if no platforms are connected
3. **Display**: Shows connected platforms with disconnect buttons using `PlatformCard` component
4. **Disconnect**: Confirmation modal → API call → optimistic UI update → success/error feedback

### API Integration

#### GET `/api/connection-data/connected` - Connected Platforms
**Purpose**: Fetch only connected platforms for disconnect management
**Features**:
- Direct PostgreSQL query for connected platforms only
- Returns array of connected platforms with display names
- Filters out platforms without names (truly connected only)
- Includes connection type for Telegram (bot vs business)
- No caching - fresh data on each request

**Response Format**:
```typescript
{
  success: boolean,
  body: {
    connectedPlatforms: Array<{
      platform: 'facebook' | 'instagram' | 'telegram' | 'web',
      displayName: string,
      connectionType?: 'bot' | 'business' // Telegram only
    }>
  }
}
```

#### POST `/api/platform/disconnect` - Platform Disconnection
**Purpose**: Disconnect platform by clearing credentials and setting status to 0
**Features**:
- Platform-specific credential clearing (tokens, IDs, names)
- Sets platform status to 0 (disabled)
- Uses N8N webhook pattern for database operations
- Handles platform validation and error responses

**Database Operations** (per platform):
- **Facebook**: Clears `fb_name`, `fb_id`, `fb_token`, sets `fb_status = 0`
- **Instagram**: Clears `ig_name`, `ig_id`, `ig_token`, sets `ig_status = 0`
- **Telegram**: Clears `tg_name`, `tg_id`, `tg_id_name`, `tg_token`, `tg_biz_id`, sets `tg_status = 0`
- **Web**: Clears `web_domain`, `web_name`, sets `web_status = 0`

### State Management
- **Connected Platforms**: Local state tracking with optimistic updates
- **Delete Confirmation**: Modal state for disconnect confirmation
- **Loading States**: Prevent multiple simultaneous operations
- **Error Handling**: Overlay system with auto-dismiss functionality

### Key Differences from Main Connect Page
- **Focused Scope**: Only disconnection, no connection or toggle functionality
- **Filtered Display**: Shows only connected platforms
- **Specialized API**: Uses `/connected` endpoint instead of full `/connection-data`
- **Simplified Flow**: No connection limits or form management needed

## Implementation Status

### Core Features ✅
- Platform connection management (Facebook, Instagram, Telegram, Web)
- Connection limit enforcement based on subscription plans
- Enable/disable platform status with billing validation
- Real-time validation and error handling
- Multi-modal confirmation dialogs
- Webhook URL management and copy functionality
- **EditConnection page for disconnect management**

### Integration Points ✅
- **Database**: Direct PostgreSQL queries for data fetching + N8N webhooks for platform operations
- **Caching**: Dashboard cache for plan data, direct queries for connection data
- **Authentication**: Supabase Auth with custom verification
- **State Management**: Complex multi-state coordination patterns
- **UI Components**: Reusable platform cards and confirmation modals

### Key Patterns
1. Always validate connection limits before allowing new connections
2. Use ref-based form management for secure token handling
3. Implement proper modal state coordination to prevent conflicts
4. Use optimistic updates with error recovery for better UX
5. Sanitize all API responses to prevent sensitive data exposure
6. Implement proper body scroll management for modal interactions
7. Use client-side hydration checks to prevent SSR issues
8. Map backend error codes to user-friendly messages
9. Implement auto-dismiss patterns for success/error messages
10. Use platform-specific validation patterns for different connection types
11. **Use specialized endpoints for focused functionality (connected vs full connection data)**
12. **Implement graceful redirects when no relevant data exists**