{"name": "chhlat-bot", "version": "0.1.0", "private": true, "scripts": {"dev": "kill -9 $(lsof -t -i:3000) 2>/dev/null || true && next dev", "build": "next build", "start": "next start -H 0.0.0.0 -p 3000", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@heroicons/react": "^2.2.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@types/jsonwebtoken": "^9.0.10", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "@upstash/redis": "^1.34.8", "browser-image-compression": "^2.0.2", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "framer-motion": "^10.16.4", "geist": "^1.4.2", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "nanoid": "^5.1.5", "next": "14.2.30", "pg": "^8.16.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-h5-audio-player": "^3.10.0-rc.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.5.2", "recordrtc": "^5.6.2", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/fluent-ffmpeg": "^2.1.27", "@types/node": "^20.11.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.15", "@types/recordrtc": "^5.6.14", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}}