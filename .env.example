# Supabase Configuration (New API Keys - 2025)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_KEY=your_supabase_publishable_key  # sb_publishable_...
SUPABASE_SECRET_KEY=your_supabase_secret_key                        # sb_secret_...

# Legacy Supabase Keys (Keep as backup during migration)
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
# SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key


# Upstash Redis Configuration
UPSTASH_REDIS_REST_URL=your_upstash_redis_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token


# Cloudflare R2 Configuration
CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
CLOUDFLARE_R2_ACCESS_KEY_ID=your_r2_access_key_id
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
CLOUDFLARE_R2_BUCKET_NAME=your_r2_bucket_name
CLOUDFLARE_R2_PUBLIC_URL=https://your-custom-domain.com

# Other Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000


CHHLAT_DB_WEBHOOK_URL=db_webhook_url
CHHLAT_DB_WEBHOOK_TOKEN=db_webhook_token

TELEGRAM_AUDIO_BOT_TOKEN=your_audio_url

# PostgreSQL Database - Direct database connections
POSTGRES_HOST=                          # PostgreSQL server hostname/IP
POSTGRES_PORT=5432                      # PostgreSQL server port
POSTGRES_USER=                          # PostgreSQL username
POSTGRES_PASSWORD=                      # PostgreSQL password
POSTGRES_DATABASE=                      # PostgreSQL database name
POSTGRES_SSL=require                    # SSL mode (require/prefer/disable)